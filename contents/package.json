{"name": "@bika/contents", "author": "", "devDependencies": {"@types/nodemailer": "^6.4.14", "@types/pluralize": "^0.0.33"}, "dependencies": {"@bika/server-orm": "workspace:*", "@bika/types": "workspace:*", "@formatjs/intl-localematcher": "^0.5.2", "@react-email/components": "0.0.28", "@toolsdk.ai/sdk-ts": "workspace:*", "lodash": "^4.17.21", "nanoid": "^5.0.9", "nodemailer": "^6.9.8", "pluralize": "^8.0.0", "react": "18.3.1", "react-email": "3.0.2", "react-markdown": "^9.0.1", "rehype-raw": "^7.0.0", "resend": "2.1.0", "sharelib": "workspace:*", "zod": "^3.24.5", "zod-to-json-schema": "^3.24.5"}, "exports": {"./testimonials/*": "./testimonials/*.ts", "./docs/*": "./docs/*.ts", "./docs/components/*": "./docs/components/*.tsx", "./docs/help": "./docs/help", "./docs/help/*": "./docs/help/*", "./docs/blog": "./docs/blog", "./docs/blog/*": "./docs/blog/*", "./templates": "./templates", "./templates/*": "./templates/*.ts", "./pages": "./pages", "./pages/*": "./pages/*.ts", "./i18n": "./i18n/index.ts", "./i18n/mobile": "./i18n/mobile.ts", "./i18n/config": "./i18n/config.ts", "./i18n/context": "./i18n/context.tsx", "./i18n/translate": "./i18n/translate.ts", "./i18n/dictionaries": "./i18n/dictionaries/index.ts", "./i18n/server": "./i18n/server.ts", "./email/*": "./email/emails/*.tsx", "./utils": "./utils/index.ts", "./utils/*": "./utils/*.ts", "./components/*": "./components/*.tsx", "./config/server": "./config/server/index.ts", "./config/server/*": "./config/server/*.ts", "./config/server/error": "./config/server/error/index.ts", "./config/server/pricing/sku/sku.json": "./config/server/pricing/sku/sku.json", "./config/client": "./config/client/index.ts", "./config/mobile": "./config/mobile/index.ts", "./config/client/*": "./config/client/*.ts", "./config/client/ai/launcher": "./config/client/ai/launcher/index.ts", "./config/client/ai/launcher/launcher-commands": "./config/client/ai/launcher/launcher-commands/index.ts", "./config/client/ai": "./config/client/ai/index.ts", "./config/client/wizard/*": "./config/client/wizard/*.ts", "./config/client/sitemap": "./config/client/sitemap.ts"}, "scripts": {"dev": "email dev --dir email/emails", "build": "node esbuild.mjs", "check": "NODE_OPTIONS=--max-old-space-size=8192 tsc --noEmit", "lint": "eslint --quiet --fix --ext js,ts,tsx ."}, "keywords": [], "license": "ISC", "private": true, "version": "1.9.0-alpha.19"}