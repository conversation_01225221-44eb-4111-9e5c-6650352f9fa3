import type { PresetLanguageAIModelDef } from '@bika/types/ai/bo';
import { type iString } from '@bika/types/system';
import { AvatarLogo } from '@bika/types/system';

export type IAIModelClientConfigTag = 'agent' | 'automation' | 'database-ai-field';
export type IAIModelClientConfig = {
  name: iString;
  description: iString;
  logo?: AvatarLogo;

  kind?: 'chat' | 'image';

  display?: 'HIDDEN' | 'DISPLAY' | 'COMING_SOON';

  // 分类识别
  tags?: IAIModelClientConfigTag[];
};

/**
 * 配置AI Model 客户端显示配置，独立前端配置给前端编译打包
 */
export const AILanguageModelClientConfig: Record<PresetLanguageAIModelDef, IAIModelClientConfig> = {
  'gpt-3.5': {
    name: 'GPT-3.5 Turbo',
    description: "OpenAI's GPT-3.5 Turbo model for general-purpose conversations",
    tags: ['agent', 'automation'],
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  'gpt-4o-mini': {
    name: 'GPT-4o Mini',
    description: 'Compact version of GPT-4o with faster response times',
    tags: ['agent', 'automation'],
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  'gpt-4o': {
    name: 'GPT-4o',
    description: "OpenAI's most advanced multimodal model",
    tags: ['agent', 'automation'],
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  'gpt-4.1': {
    name: 'GPT-4.1',
    description: 'Latest GPT-4.1 model with enhanced capabilities',
    tags: ['agent', 'automation'],
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  'gpt-4.1-mini': {
    name: 'GPT-4.1 Mini',
    description: 'Lightweight version of GPT-4.1 for faster processing',
    tags: ['agent', 'automation'],
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  // 'gpt-image-1': {
  //   name: 'GPT Image-1',
  //   description: 'Specialized model for image generation and processing',
  // },
  'azure/gpt-4o': {
    name: 'Azure GPT-4o',
    description: 'GPT-4o model hosted on Azure OpenAI Service',
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  'azure/gpt-4o-mini': {
    name: 'Azure GPT-4o Mini',
    description: 'GPT-4o Mini model hosted on Azure OpenAI Service',
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  'azure/gpt-4.1': {
    name: 'Azure GPT-4.1',
    description: 'GPT-4.1 model hosted on Azure OpenAI Service',
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  // 'azure/gpt-image-1': {
  //   name: 'Azure GPT Image-1',
  //   description: 'Image generation model hosted on Azure OpenAI Service',
  // },
  doubao: {
    name: 'Doubao Lite',
    description: "ByteDance's Doubao 1.5 Lite model with 32k context",
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  'doubao-pro-32k': {
    name: 'Doubao Pro 32K',
    description: "ByteDance's Doubao 1.5 Pro model with 32k context window",
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  'doubao-pro-256k': {
    name: 'Doubao Pro 256K',
    description: "ByteDance's Doubao 1.5 Pro model with 256k context window",
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  'deepseek-r1': {
    name: 'DeepSeek R1',
    description: "DeepSeek's reasoning-focused R1 model",
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  'deepseek-v3': {
    name: 'DeepSeek V3',
    description: "DeepSeek's latest V3 model with advanced capabilities",
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  'qwen3-coder-plus': {
    name: 'Qwen 3 Coder Plus',
    description: "Alibaba's Qwen 3 Coder Plus model for coding tasks",
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  'qwen-plus': {
    name: 'Qwen Plus',
    description: "Alibaba's Qwen Plus model for enhanced performance",
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  'qwen-turbo': {
    name: 'Qwen Turbo',
    description: "Alibaba's Qwen Turbo model optimized for speed",
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  'siliconflow/DeepSeek-V3': {
    name: 'SiliconFlow DeepSeek V3',
    description: 'DeepSeek V3 model via SiliconFlow platform',
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  'siliconflow/DeepSeek-R1': {
    name: 'SiliconFlow DeepSeek R1',
    description: 'DeepSeek R1 reasoning model via SiliconFlow platform',
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  'siliconflow/DeepSeek-R1-Distill-Qwen-7B': {
    name: 'SiliconFlow DeepSeek R1 Distill',
    description: 'Distilled 7B version of DeepSeek R1 via SiliconFlow',
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  'gemini-pro': {
    name: 'Gemini Pro',
    description: "Google's Gemini 1.5 Pro multimodal model",
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  'gemini-flash': {
    name: 'Gemini Flash',
    description: "Google's Gemini 1.5 Flash model optimized for speed",
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  gptproto: {
    name: 'GPTProto GPT-4o',
    description: 'GPT-4o model via GPTProto service',
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  'claude-3-sonnet': {
    name: 'Claude 3 Sonnet',
    description: "Anthropic's Claude 3 Sonnet model via AWS Bedrock",
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  'claude-3-7-sonnet': {
    name: 'Claude 3.7 Sonnet',
    description: "Anthropic's Claude 3.7 Sonnet model via AWS Bedrock",
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  'claude-opus-4': {
    name: 'Claude Opus 4',
    description: "Anthropic's most powerful Claude Opus 4 model via AWS Bedrock",
    tags: ['agent', 'automation'],
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  'claude-sonnet-4': {
    name: 'Claude Sonnet 4',
    description: "Anthropic's Claude Sonnet 4 model via AWS Bedrock",
    tags: ['agent', 'automation'],
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  mock: {
    name: '',
    description: '',
    kind: undefined,
    display: 'HIDDEN',
    tags: undefined,
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
};

export function getAILanguageModelClientConfigs(
  tag: IAIModelClientConfigTag,
): { key: PresetLanguageAIModelDef; config: IAIModelClientConfig }[] {
  return Object.entries(AILanguageModelClientConfig)
    .filter(([, config]) => config.tags?.includes(tag))
    .map(([key, config]) => ({ key: key as PresetLanguageAIModelDef, config }));
}
