const dict = {
  toolsdk: {
    website: {
      title: 'AI Tools, 1 Line of Code',
      description: 'A free TypeScript SDK for building agentic AI apps — with instant access to MCP servers.',
    },
    component: {
      codeSection: {
        title: 'Quick Starting',
        description: 'More details and complete examples on',
        examples: {
          AISDK: {
            comment: {
              initialize: 'Initialize ToolSDK',
              useAISDK: 'Use AI SDK to call tools',
            },
          },
          OPENAI: {
            comment: {
              initialize: 'Initialize OpenAI and ToolSDK',
              callChatGPT: 'Call ChatGPT and handle tool calls',
              handleToolCalls: 'Handle tool calls',
              executeTool: 'Execute tool using ToolSDK',
            },
          },
        },
      },
    },
  },
};

export default dict;
