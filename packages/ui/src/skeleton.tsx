import Box from '@mui/joy/Box';
import Grid from '@mui/joy/Grid';
import MUISkeleton, { type SkeletonProps } from '@mui/joy/Skeleton';
import Stack from '@mui/joy/Stack';
import {
  type SkeletonPositionType,
  LoadingSkeletonConfig,
  getNodePrefix,
} from '@bika/contents/config/client/ui/loading-skeleton';
import { Divider } from './layout-components';

interface Props {
  pos: SkeletonPositionType;
  type?: string;
}

const CustomSkeleton = (props: SkeletonProps) => (
  <MUISkeleton
    {...props}
    sx={{
      ...props.sx,
      background: 'transparent',
      '&:before': {
        background: 'rgba(190,190,190,.12)',
      },
    }}
    animation="wave"
  />
);

const RecordDetailSkeleton = () => (
  <Stack useFlexGap sx={{ maxWidth: '100%', minHeight: '650px' }}>
    <Stack sx={{ width: '100%' }} spacing={'12px'} direction={'column'}>
      <Box sx={{ width: '33%' }}>
        <CustomSkeleton variant="text" level="h2" />
      </Box>

      <CustomSkeleton variant="text" level="h1" />

      <CustomSkeleton variant="text" level="h1" />

      <Box sx={{ width: '33%' }}>
        <CustomSkeleton variant="text" level="h2" />
      </Box>
    </Stack>
  </Stack>
);
const SidebarSkeleton = () => (
  <Box pl={2} pt={2}>
    <CustomSkeleton variant="rectangular" height={24} width={120} />
    <Box display="flex" gap={1} my={1} alignItems="center">
      <CustomSkeleton variant="rectangular" height={32} width={32} />
      <CustomSkeleton variant="text" height={16} width="calc(100% - 32px)" />
    </Box>
    <Box display="flex" gap={1} my={1} alignItems="center">
      <CustomSkeleton variant="rectangular" height={32} width={32} />
      <CustomSkeleton variant="text" height={16} width="calc(100% - 32px)" />
    </Box>
    <Box display="flex" gap={1} my={1} alignItems="center">
      <CustomSkeleton variant="rectangular" height={32} width={32} />
      <CustomSkeleton variant="text" height={16} width="calc(100% - 32px)" />
    </Box>
  </Box>
);

const NodePageSkeletonComponent = () => (
  <Stack height="100%" spacing={2} sx={{ p: 2, boxSizing: 'border-box', px: '24px' }}>
    <CustomSkeleton variant="rectangular" width="100%" height={20} sx={{ my: '14px' }} />
    <Divider />

    <CustomSkeleton variant="rectangular" width="20%" height={64} sx={{ my: '16px' }} />
    <CustomSkeleton variant="rectangular" width="100%" height={64} sx={{ my: '16px' }} />
    <CustomSkeleton variant="rectangular" width="100%" height={64} sx={{ my: '16px' }} />
  </Stack>
);

export const FieldSkeleton = () => (
  <Box pt={2}>
    <CustomSkeleton variant="text" width="120px" height="32" sx={{ mb: 0.5 }} />
    <CustomSkeleton variant="text" width="100%" height="32" level="h3" />
  </Box>
);

const TemplateSkeleton = () => (
  <Box sx={{ overflow: 'hidden' }}>
    <CustomSkeleton variant="text" width="120px" height="32" level="h3" sx={{ mb: 0.5 }} />
    <CustomSkeleton variant="text" width="240px" height="32" sx={{ mb: 1 }} />
    <Grid container spacing={2} sx={{ flexGrow: 1 }}>
      <Grid xs={4}>
        <CustomSkeleton variant="rectangular" width="100%" height="120px" />
      </Grid>
      <Grid xs={4}>
        <CustomSkeleton variant="rectangular" width="100%" height="120px" />
      </Grid>
      <Grid xs={4}>
        <CustomSkeleton variant="rectangular" width="100%" height="120px" />
      </Grid>
      <Grid xs={4}>
        <CustomSkeleton variant="rectangular" width="100%" height="120px" />
      </Grid>
      <Grid xs={4}>
        <CustomSkeleton variant="rectangular" width="100%" height="120px" />
      </Grid>
      <Grid xs={4}>
        <CustomSkeleton variant="rectangular" width="100%" height="120px" />
      </Grid>
      <Grid xs={4}>
        <CustomSkeleton variant="rectangular" width="100%" height="120px" />
      </Grid>
      <Grid xs={4}>
        <CustomSkeleton variant="rectangular" width="100%" height="120px" />
      </Grid>
      <Grid xs={4}>
        <CustomSkeleton variant="rectangular" width="100%" height="120px" />
      </Grid>
    </Grid>
  </Box>
);
const NoteSkeleton = () => (
  <>
    <CustomSkeleton variant="text" />
    <CustomSkeleton variant="text" />
    <CustomSkeleton variant="text" />
  </>
);

const ShareSkeleton = () => (
  <div className={'space-y-3 h-[520px]'}>
    <CustomSkeleton variant="text" level="h1" width={'200px'} />
    <CustomSkeleton variant="text" level="h1" />
    <CustomSkeleton variant="text" level="h1" />
    <CustomSkeleton variant="text" level="h1" />
  </div>
);

const NodeLoading = () => (
  <Box sx={{ p: 2 }}>
    <CustomSkeleton variant="rectangular" width="100%" height={48} sx={{ mb: 1 }} />
    <CustomSkeleton variant="rectangular" width="100%" height={48} sx={{ mb: 1 }} />
    <CustomSkeleton variant="rectangular" width="100%" height={48} />
  </Box>
);

const SpaceHomeSkeleton = () => (
  <Stack height="100%" spacing={2} sx={{ p: 2, boxSizing: 'border-box', px: '24px' }}>
    <CustomSkeleton variant="rectangular" width="100%" height={20} sx={{ my: '14px' }} />
    <Divider />

    <CustomSkeleton variant="rectangular" width="100%" height={200} />
    <CustomSkeleton variant="rectangular" width="100%" height={148} />

    <Stack direction="row" spacing={2} position="relative" flexGrow={1}>
      <CustomSkeleton variant="inline" width="50%" />
      <CustomSkeleton variant="inline" width="50%" />
    </Stack>
  </Stack>
);

const ReportList = () => (
  <Stack height="100%" spacing={2} sx={{ p: 2, boxSizing: 'border-box', px: '24px' }}>
    <CustomSkeleton variant="rectangular" width="100%" height={20} sx={{ my: '14px' }} />
    <Divider />

    <CustomSkeleton variant="rectangular" width="100%" height={48} />
    <CustomSkeleton variant="rectangular" width="100%" height={48} />
    <CustomSkeleton variant="rectangular" width="100%" height={48} />
  </Stack>
);

const ModalDefault = () => (
  <>
    <CustomSkeleton variant="rectangular" width="20%" height={32} sx={{ mb: 2 }} />
    <CustomSkeleton variant="rectangular" width="100%" height={32} sx={{ mb: 2 }} />
    <CustomSkeleton variant="rectangular" width="100%" height={32} sx={{ mb: 2 }} />
    <CustomSkeleton variant="rectangular" width="100%" height={32} sx={{ mb: 2 }} />
  </>
);

const MinHeightModal = () => (
  <Box height="640px">
    <CustomSkeleton variant="rectangular" width="20%" height={32} sx={{ mb: 2 }} />
    <CustomSkeleton variant="rectangular" width="100%" height={32} sx={{ mb: 2 }} />
    <CustomSkeleton variant="rectangular" width="100%" height={32} sx={{ mb: 2 }} />
    <CustomSkeleton variant="rectangular" width="100%" height={32} sx={{ mb: 2 }} />
  </Box>
);

const Folder = () => (
  <Box width="100%" height="100%" padding="24px">
    <CustomSkeleton
      sx={{
        my: 3,
      }}
      variant="rectangular"
      width={'100%'}
      height={20}
    />
    <Divider sx={{ mb: 3 }} />
    <Stack spacing={3} direction="row" mb={3}>
      <CustomSkeleton sx={{ flexShrink: 0 }} variant="rectangular" width={180} height={180} />
      <Stack width={'100%'} spacing={3}>
        <CustomSkeleton variant="rectangular" width={'40%'} height={40} />
        <CustomSkeleton variant="rectangular" width={'100%'} height={40} />
        <CustomSkeleton variant="rectangular" width={'100%'} height={40} />
      </Stack>
    </Stack>
    <Stack spacing={3}>
      <CustomSkeleton height={40} variant="rectangular" width="40%" />
      <CustomSkeleton height={40} variant="rectangular" width="100%" />
      <CustomSkeleton height={40} variant="rectangular" width="100%" />
      <CustomSkeleton height={40} variant="rectangular" width="100%" />
    </Stack>
  </Box>
);

const SpaceSetting = () => (
  <>
    <CustomSkeleton height={40} variant="text" width="40%" />
    <CustomSkeleton height={40} variant="text" width="100%" />
    <CustomSkeleton height={40} variant="text" width="100%" />
    <CustomSkeleton height={40} variant="text" width="40%" />
  </>
);

const EditorSetting = () => (
  <Stack spacing={2} padding={2}>
    <CustomSkeleton height={40} variant="rectangular" width="80%" />
    <CustomSkeleton height={40} variant="rectangular" width="100%" />
    <CustomSkeleton height={40} variant="rectangular" width="100%" />
  </Stack>
);

const AILauncher = () => (
  <>
    <CustomSkeleton level="h1" variant="text" sx={{ my: '16px' }} />
    <Stack spacing={2}>
      <CustomSkeleton level="h2" variant="text" width="40%" />
      <CustomSkeleton level="h2" variant="text" width="100%" />
      <CustomSkeleton level="h2" variant="text" width="100%" />
    </Stack>
  </>
);

const AutomationDetail = () => (
  <Stack height="100%" spacing={2} sx={{ p: 2, boxSizing: 'border-box', px: '24px' }}>
    <CustomSkeleton variant="rectangular" width="100%" height={20} sx={{ my: '14px' }} />
    <Divider />
    <Stack alignItems="center" pt="40px">
      <Box width="400px">
        <CustomSkeleton variant="rectangular" width="60%" height={40} />
        <CustomSkeleton variant="rectangular" width="100%" height={80} sx={{ mt: '16px', mb: '32px' }} />
        <CustomSkeleton variant="rectangular" width="60%" height={40} />
        <CustomSkeleton variant="rectangular" width="100%" height={80} sx={{ mt: '16px', mb: '32px' }} />
      </Box>
    </Stack>
  </Stack>
);

const CommentList = () => (
  <Stack height="100%" spacing={2} sx={{ p: 2, boxSizing: 'border-box', px: '24px' }}>
    <Stack spacing={1}>
      <Stack direction="row" spacing={2}>
        <CustomSkeleton variant="circular" width={40} height={40} sx={{ flex: 'none' }} />
        <Stack spacing={0.5} width="calc(100% - 52px)">
          <CustomSkeleton variant="rectangular" width="100%" height={22} sx={{ my: '14px' }} />
          <CustomSkeleton variant="rectangular" width="100%" height={14} sx={{ my: '14px' }} />
        </Stack>
      </Stack>
      <CustomSkeleton variant="rectangular" width="100%" height={20} />
      <CustomSkeleton variant="rectangular" width="100%" height={20} />
      <CustomSkeleton variant="rectangular" width="100%" height={20} />
    </Stack>
    <Stack spacing={1}>
      <Stack direction="row" spacing={2}>
        <CustomSkeleton variant="circular" width={40} height={40} sx={{ flex: 'none' }} />
        <Stack spacing={0.5} width="calc(100% - 52px)">
          <CustomSkeleton variant="rectangular" width="100%" height={22} sx={{ my: '14px' }} />
          <CustomSkeleton variant="rectangular" width="100%" height={14} sx={{ my: '14px' }} />
        </Stack>
      </Stack>
      <CustomSkeleton variant="rectangular" width="100%" height={20} />
      <CustomSkeleton variant="rectangular" width="100%" height={20} />
      <CustomSkeleton variant="rectangular" width="100%" height={20} />
    </Stack>
  </Stack>
);

const DashboardSkeleton = () => (
  <Stack height="100%" spacing={2} sx={{ p: 2, boxSizing: 'border-box', px: '24px' }}>
    <CustomSkeleton variant="rectangular" width="100%" height={20} sx={{ my: '14px' }} />
    <Divider />
    <Grid container columns={12} spacing={3}>
      {[...Array(4)].map((_, index) => (
        <Grid key={index} xs={6}>
          <CustomSkeleton
            variant="rectangular"
            width="100%"
            height={160}
            sx={{
              borderRadius: '8px',
            }}
          />
        </Grid>
      ))}
    </Grid>
  </Stack>
);

const AIWizard = () => (
  <Stack direction="row" spacing={1} p={3}>
    <Box>
      <CustomSkeleton variant="circular" width={32} height={32} />
    </Box>
    <Stack width="100%" spacing={1}>
      <CustomSkeleton variant="rectangular" width="90%" height={32} sx={{ borderRadius: '4px' }} />
      <CustomSkeleton variant="rectangular" width="75%" height={32} sx={{ borderRadius: '4px' }} />
      <CustomSkeleton variant="rectangular" width="60%" height={32} sx={{ borderRadius: '4px' }} />
    </Stack>
  </Stack>
);

// Web Page Skeleton for web pages, skeletons for a "blog", includes title, description, content area and etc.
const BlogPageSkeleton = () => (
  <Stack spacing={1} p={3}>
    {/* Header/Navigation */}
    <CustomSkeleton variant="rectangular" width="100%" height={40} />

    {/* Blog Title */}
    <CustomSkeleton variant="text" width="70%" height={48} level="h1" />

    {/* Meta info (date, author, etc.) */}
    <Stack direction="row" spacing={2} alignItems="center">
      <CustomSkeleton variant="circular" width={32} height={32} />
      <CustomSkeleton variant="text" width="150px" height={16} />
      <CustomSkeleton variant="text" width="100px" height={16} />
    </Stack>

    {/* Featured image */}
    <CustomSkeleton variant="rectangular" width="100%" height={200} sx={{ borderRadius: '8px' }} />

    {/* Content paragraphs */}
    <Stack spacing={2}>
      <CustomSkeleton variant="text" width="100%" height={20} />
      <CustomSkeleton variant="text" width="100%" height={20} />
      <CustomSkeleton variant="text" width="85%" height={20} />
      <CustomSkeleton variant="text" width="100%" height={20} />
      <CustomSkeleton variant="text" width="90%" height={20} />
    </Stack>

    {/* Section heading */}
    <CustomSkeleton variant="text" width="40%" height={32} level="h2" sx={{ mt: 2 }} />

    {/* More content */}
    <Stack spacing={2}>
      <CustomSkeleton variant="text" width="100%" height={20} />
      <CustomSkeleton variant="text" width="95%" height={20} />
      <CustomSkeleton variant="text" width="100%" height={20} />
    </Stack>

    {/* Tags/Categories */}
    <Stack direction="row" spacing={1} sx={{ mt: 2 }}>
      <CustomSkeleton variant="rectangular" width={60} height={24} sx={{ borderRadius: '12px' }} />
      t'm
      <CustomSkeleton variant="rectangular" width={80} height={24} sx={{ borderRadius: '12px' }} />
      <CustomSkeleton variant="rectangular" width={70} height={24} sx={{ borderRadius: '12px' }} />
    </Stack>
  </Stack>
);

const CardSelectorSkeleton = () => (
  <Stack direction="row" spacing={2} sx={{ mt: 2 }}>
    <CustomSkeleton variant="rectangular" width={168} height={110} sx={{ borderRadius: '12px' }} />
    <CustomSkeleton variant="rectangular" width={168} height={110} sx={{ borderRadius: '12px' }} />
    <CustomSkeleton variant="rectangular" width={168} height={110} sx={{ borderRadius: '12px' }} />
    <CustomSkeleton variant="rectangular" width={168} height={110} sx={{ borderRadius: '12px' }} />
  </Stack>
);

const ChatSkeleton = () => (
  <Stack
    height="100%"
    alignItems={'center'}
    spacing={'24px'}
    sx={{ p: '24px 16px', boxSizing: 'border-box', px: '24px' }}
  >
    <Stack spacing={2} direction="row-reverse" alignItems="flex-start" sx={{ width: '100%', maxWidth: '720px' }}>
      <CustomSkeleton
        variant="rectangular"
        width={32}
        height={32}
        sx={{ flex: 'none', borderRadius: '8px', position: 'relative', top: '7px' }}
      />
      <CustomSkeleton variant="rectangular" width="75%" height={46} sx={{ my: '14px', borderRadius: '8px' }} />
    </Stack>
    <Stack spacing={2} direction="row" alignItems="flex-start" sx={{ width: '100%', maxWidth: '720px' }}>
      <CustomSkeleton
        variant="rectangular"
        width={32}
        height={32}
        sx={{ flex: 'none', borderRadius: '8px', position: 'relative', top: '7px' }}
      />
      <CustomSkeleton variant="rectangular" width="100%" height={92} sx={{ my: '14px', borderRadius: '8px' }} />
    </Stack>
    <Stack spacing={2} direction="row-reverse" alignItems="flex-start" sx={{ width: '100%', maxWidth: '720px' }}>
      <CustomSkeleton
        variant="rectangular"
        width={32}
        height={32}
        sx={{ flex: 'none', borderRadius: '8px', position: 'relative', top: '7px' }}
      />
      <CustomSkeleton variant="rectangular" width="50%" height={46} sx={{ my: '14px', borderRadius: '8px' }} />
    </Stack>
  </Stack>
);

export function Skeleton(props: Props) {
  const nodeSkeleton = props.type ? getNodePrefix(props.type) : null;
  const uiSkeleton = nodeSkeleton || LoadingSkeletonConfig[props.pos].skeleton;

  switch (uiSkeleton) {
    case 'SELECTOR':
      return <CardSelectorSkeleton />;
    case 'NONE':
      return null;
    case 'NODE_LOADING':
    case 'NODE_INFO':
      return <NodeLoading />;
    case 'SPACE_SIDEBAR':
      return <SidebarSkeleton />;
    case 'RECORD_DETAIL':
      return <RecordDetailSkeleton />;
    case 'NOTE':
      return <NoteSkeleton />;
    case 'SHARE':
      return <ShareSkeleton />;
    case 'TEMPLATES_CENTER':
      return <TemplateSkeleton />;
    case 'NODE_PAGE':
      return <NodePageSkeletonComponent />;
    case 'BLOG':
      return <BlogPageSkeleton />;
    case 'SPACE_HOME':
      return <SpaceHomeSkeleton />;
    case 'REPORTS_LIST':
      return <ReportList />;
    case 'MIN_HEIGHT_MODAL':
      return <MinHeightModal />;
    case 'MODAL_SKELETON':
      return <ModalDefault />;
    case 'FOLDER':
    case 'fold':
    case 'nodtpl':
      return <Folder />;
    case 'SPACE_SETTING':
      return <SpaceSetting />;
    case 'AI_LAUNCHER':
      return <AILauncher />;
    case 'AI_WIZARD':
      return <AIWizard />;
    case 'CHAT':
      return <ChatSkeleton />;
    case 'EDITOR_SETTING':
      return <EditorSetting />;
    case 'AUTOMATION_DETAIL':
    case 'ato':
      return <AutomationDetail />;
    case 'dsb':
    case 'DASHBOARD':
      return <DashboardSkeleton />;
    // node_page根据前缀判断需要展示的skeleton
    case 'fom':
    case 'mir':
    case 'dat':
    case 'doc':
      return <NodePageSkeletonComponent />;
    case 'COMMENT_LIST':
      return <CommentList />;

    default:
      // "default"
      return (
        <Box height="200px">
          <CustomSkeleton height="200px" />
        </Box>
      );
  }
}
