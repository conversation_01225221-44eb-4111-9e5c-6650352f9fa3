'use client';

import { Typo<PERSON>, Chip } from '@mui/joy';
import Option from '@mui/joy/Option';
import Select from '@mui/joy/Select';
import type { SxProps } from '@mui/joy/styles/types';
import React, { useState, useEffect } from 'react';
import { useLocale } from '@bika/contents/i18n';
import type { AvatarLogo } from '@bika/types/system';
import CheckOutlined from '@bika/ui/icons/components/check_outlined';
import { SelectInputComponent } from './select-input-component';
import { FormHelperText, FormLabel, FormControl } from '../../form-components';
import { Box } from '../../layout-components';
import { NodeIcon } from '../../node/icon';

export { SelectInputComponent as SelectInputV2 } from './select-input-component';

export { Option };

export type OptionType = {
  label: string;
  value: string;
  disabled?: boolean;
  icon?: React.ReactNode | string | AvatarLogo;
  description?: string;
  tooltip?: {
    title?: string;
    content?: string;
    disabledTips?: string;
  };
};

export interface SelectInputProps<T> {
  options: OptionType[];
  label?: string;
  hideSearch?: boolean;

  emptyMsg?: string;
  // 用于下拉列表隐藏某些选项, hiddenOptions?.includes(option.value)
  hiddenOptions?: string[];
  autoFocus?: boolean;

  disabledTips?: string;
  placeholder?: React.ReactNode;
  value: T | null;
  onChange: (newValue: T | null) => void;
  selectedEmptyContent?: React.ReactNode;
  required?: boolean;
  disabled?: boolean;
  footer?: React.ReactNode;
  handleSearch?: (searchTerm: string) => void;
  hidden?: boolean;
  setErrors?: (errors?: Record<string, string>) => void;
  iconSize?: 32 | 24 | 16;

  helpLink?: {
    text: string | React.ReactNode;
    url: string;
    icon?: React.ReactNode;
  };
  helpText?: string | React.ReactNode;
  errorTips?: string | React.ReactNode;

  classes?: {
    root?: SxProps;
    joySelect?: SxProps;
    itemContentSx?: SxProps;
    popoverContainer?: SxProps;
  };

  sx?: SxProps;
  id?: string;
  hideLabel?: boolean;
}

export { SelectInputComponent as SelectInput };

interface MultiProps<T> {
  options: { label: React.ReactNode; value: T; disabled?: boolean; icon: OptionType['icon'] }[];
  label?: string;
  placeholder?: React.ReactNode;
  value: T[];
  onChange: (newValue: T[]) => void;
  required?: boolean;
  setErrors?: (errors?: Record<string, string>) => void;
}
export function SelectMultiInput<T extends string>(props: MultiProps<T>) {
  const { setErrors, required } = props;
  const { t } = useLocale();

  const [internalError, setInternalError] = useState<string | null>(null);

  useEffect(() => {
    const errorKey = 'SelectMultiInput';
    if (required && setErrors) {
      const error = props.value.length > 0 ? '' : t.automation.trigger.scheduler.at_least_one_option;
      setErrors({ [errorKey]: error });
      setInternalError(error);
    }
    return () => {
      if (required && setErrors) {
        setErrors({ [errorKey]: '' });
        setInternalError(null);
      }
    };
  }, [props.value.length, setErrors, required]);

  return (
    <FormControl sx={{ mt: 1 }}>
      <FormLabel required={required}>{props.label || 'Multi Select'}</FormLabel>

      <Select
        sx={{
          '& .MuiBox-root': {
            flexWrap: 'wrap',
          },
        }}
        multiple
        placeholder={props.placeholder}
        value={props.value}
        onChange={(_event, value) => {
          props.onChange(value);
        }}
        renderValue={(selected) => (
          <Box sx={{ display: 'flex', gap: '0.25rem' }}>
            {selected.map((selectedOption, index) => (
              <Chip variant="soft" color="neutral" key={index}>
                {selectedOption.label}
              </Chip>
            ))}
          </Box>
        )}
      >
        {props.options.map((item) => {
          let icon: React.ReactNode;
          if (typeof item.icon === 'string') {
            icon = (
              <NodeIcon
                value={{
                  kind: 'avatar',
                  avatar: {
                    type: 'URL',
                    url: item.icon,
                  },
                }}
              ></NodeIcon>
            );
          } else if (item.icon && typeof item.icon === 'object' && 'type' in item.icon) {
            icon = (
              <NodeIcon
                value={{
                  kind: 'avatar',
                  avatar: item.icon as AvatarLogo,
                }}
              ></NodeIcon>
            );
          } else {
            icon = item.icon;
          }

          return (
            <Option
              key={item.value}
              value={item.value}
              disabled={item.disabled}
              sx={{ justifyContent: 'space-between' }}
            >
              {icon}
              <Typography noWrap>{item.label}</Typography>
              {props.value.includes(item.value) && (
                <Typography sx={{ ml: 'auto' }}>
                  <CheckOutlined size={13} />
                </Typography>
              )}
            </Option>
          );
        })}
      </Select>
      {required && internalError && (
        <FormHelperText sx={{ color: 'var(--status-danger)', mt: 1 }}>{internalError}</FormHelperText>
      )}
    </FormControl>
  );
}

interface GeneralProps<T> {
  options: { label: React.ReactNode; value: T; disabled?: boolean }[];
  label?: string;
  placeholder?: React.ReactNode;
  value: T;
  onChange: (newValue: T) => void;
  required?: boolean;
}
export function SelectGeneralInput<T extends string>(props: GeneralProps<T>) {
  const { required } = props;
  return (
    <Box mt={1}>
      <FormLabel required={required}>{props.label || 'Select'}</FormLabel>
      <Select
        placeholder={props.placeholder}
        defaultValue={props.value}
        onChange={(_event, value) => {
          props.onChange(value as T);
        }}
      >
        {props.options.map((item) => (
          <Option key={item.value} value={item.value} disabled={item.disabled}>
            {item.label}
          </Option>
        ))}
      </Select>
    </Box>
  );
}
