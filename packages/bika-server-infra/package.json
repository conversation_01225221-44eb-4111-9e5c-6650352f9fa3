{"name": "@bika/server-infra", "private": true, "description": "", "exports": {"./session": "./src/session/index.ts", "./trpc": "./src/trpc/index.ts", "./error": "./src/error/index.ts", "./utils": "./src/utils.ts"}, "dependencies": {"@bika/contents": "workspace:*", "@bika/server-orm": "workspace:*", "@bika/types": "workspace:*", "@hono/trpc-server": "^0.3.2", "@lucia-auth/adapter-prisma": "^4.0.0", "@trpc/server": "^10.45.2", "arctic": "^1.2.0", "lucia": "^3.0.1"}, "keywords": [], "author": "", "license": "ISC", "version": "1.9.0-alpha.19"}