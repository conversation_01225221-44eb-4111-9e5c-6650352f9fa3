'use client';

import { ActionTemplateVO, PackageDetailVO } from '@toolsdk.ai/sdk-ts/types/vo';
import React from 'react';
import Stack from '@mui/joy/Stack';
import { Button } from '@bika/ui/button';
import { PackageStar } from './package-star';
import { AvatarImg, AvatarSize } from '@bika/ui/components/avatar/index';
import { iStringParse } from '@bika/types/system';
import { Typography } from '@bika/ui/texts';
import Link from 'next/link';
import { Markdown } from '@bika/ui/markdown-component';
import { PackageConfigurations } from '@toolsdk.ai/sdk-ts/react';
import { Tabs } from '@bika/ui/toolsdk/tabs';
import { trpcClient } from '@toolsdk.ai/domain/client/api-client';
import Modal from '@mui/joy/Modal';
import ModalClose from '@mui/joy/ModalClose';
import ModalDialog from '@mui/joy/ModalDialog';
import DialogTitle from '@mui/joy/DialogTitle';
import DialogContent from '@mui/joy/DialogContent';
import { WidgetRenderer } from '@toolsdk.ai/domain/client/widgets/widget-renderer';
import GotoOutlined from '@bika/ui/icons/components/goto_outlined';
import NewtabOutlined from '@bika/ui/icons/components/newtab_outlined';
import StarOutlined from '@bika/ui/icons/components/star_outlined';
import { ToolDetailRenderer } from '@toolsdk.ai/sdk-ts/react';
import { buildTrpcConfigurationApi } from '../api';

interface Props {
  value: PackageDetailVO;
}

export function PackageDetailVORenderer(props: Props) {
  const { version } = props.value;
  const [index, setIndex] = React.useState<number>(() => {
    return props.value.readme ? 0 : 1; // 默认显示 Content 标签，如果没有 readme 则显示 Tools 标签
  });

  const [result, setResult] = React.useState<string | null>(null);
  const [configurationInstanceId, setConfigurationInstanceId] = React.useState<string | undefined>(undefined);

  const tools: ActionTemplateVO[] = props.value.tools || [];

  // 定义标签项
  const tabItems = [
    {
      key: 0,
      label: 'Content',
      content: <Markdown markdown={props.value.readme || ''} />,
    },
    {
      key: 1,
      label: 'Tools',
      content: (
        <>
          {tools.map((tool, idx) => {
            return (
              <ToolDetailRenderer
                key={idx}
                run={async (inputData) => {
                  const ret = await trpcClient.packages.runTool.mutate({
                    packageKey: props.value.key,
                    packageVersion: version,
                    configurationInstanceId,
                    toolKey: tool.key,
                    inputData,
                  });
                  setResult(typeof ret === 'string' ? ret : JSON.stringify(ret, null, 2));
                }}
                tool={tool}
              />
            );
          })}
        </>
      ),
    },
  ];

  return (
    <Stack sx={{ width: '1200px', margin: '0 auto' }} direction="column" display={'flex'}>
      <Stack direction="row" spacing={2} sx={{ width: '100%' }} display={'flex'}>
        <Stack sx={{ width: '162px', height: '162px' }}>
          <AvatarImg
            avatar={
              props.value.logo || {
                type: 'COLOR',
                color: '',
              }
            }
            shape={'SQUARE'}
            customSize={162}
            name={iStringParse(props.value.name)}
            style={{ width: '100%', height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}
          />
        </Stack>
        <Stack ml={3} flex={1}>
          <Stack direction={'row'} alignItems={'center'}>
            <Typography level="h5">
              {iStringParse(props.value.name)}
              {/* ({props.value.packageType}) */}
            </Typography>
            {version && (
              <Stack
                sx={{
                  backgroundColor: 'var(--bg-controls)',
                  display: 'flex',
                  padding: '2px 4px',
                  borderRadius: '4px',
                }}
                ml={1}
              >
                <Typography level="b3" textColor="var(--text-primary)">
                  {version || '0.0.0'}
                </Typography>
              </Stack>
            )}
          </Stack>
          <Stack mb={2} direction={'row'} alignItems={'center'} spacing={1} mt={1}>
            <AvatarImg
              alt={props.value.creator.name}
              avatar={props.value.creator.avatar}
              name={props.value.creator.name}
              customSize={AvatarSize.Size24}
            />
            <Typography textColor={'var(--text-secondary)'} level="b3">
              {props.value.creator.name}
            </Typography>
          </Stack>
          {props.value.description && (
            <Stack mb={2}>
              <Typography textColor={'var(--text-secondary)'} level="b3">
                {iStringParse(props.value.description)}
              </Typography>
            </Stack>
          )}
          <Stack direction="row" spacing={2}>
            {props.value.url && (
              <Link href={props.value.url} target="_blank" rel="noopener noreferrer">
                <Button variant="soft" color="neutral">
                  <Stack spacing={1} direction="row" alignItems="center">
                    <GotoOutlined /> <span>Visit server</span>
                  </Stack>
                </Button>
              </Link>
            )}
            {props.value.validated && (
              <Link href={`https://www.npmjs.com/package/${props.value.key}`} target="_blank" rel="noopener noreferrer">
                <Button variant="soft" color="neutral">
                  <Stack spacing={1} direction="row" alignItems="center">
                    <NewtabOutlined /> <span>NPM</span>
                  </Stack>
                </Button>
              </Link>
            )}
            <PackageStar stars={{ count: props.value.stars, isStarred: props.value.isStarred }} vo={props.value} />
            <Link href="https://github.com/toolsdk-ai/awesome-mcp-registry" target="_blank" rel="noopener noreferrer">
              <Button variant="soft" color="neutral" onClick={async () => {}}>
                <Stack spacing={1} direction="row" alignItems="center">
                  <StarOutlined /> <span>Claim</span>
                </Stack>
              </Button>
            </Link>
          </Stack>
        </Stack>
      </Stack>

      {/* 左右 */}
      <Stack mt={2} sx={{ width: '100%' }} direction="row" display={'flex'} justifyContent="space-between">
        <Stack flex={1} mr={4}>
          <Tabs items={tabItems} activeKey={index} onChange={(key) => setIndex(key as number)} />
        </Stack>
        <Stack mt={8} sx={{ width: '400px' }} direction={'column'} spacing={2}>
          <Stack
            sx={{
              border: '1px solid var(--border-default)',
              background: 'var(--bg-surface)',
              borderRadius: '8px',
              p: 2,
              pt: 0,
            }}
          >
            <PackageConfigurations
              api={buildTrpcConfigurationApi()}
              packageKey={props.value.key}
              packageVersion={props.value.version}
              config={props.value.configuration}
              value={configurationInstanceId as string}
              onChange={(configurationInstanceId) => {
                setConfigurationInstanceId(configurationInstanceId);
              }}
            />
          </Stack>
        </Stack>
      </Stack>

      <Modal open={result !== null} onClose={() => setResult(null)}>
        <ModalDialog variant="plain">
          <ModalClose />
          <DialogTitle>Result</DialogTitle>
          <DialogContent sx={{ mt: 3 }}>
            <WidgetRenderer
              value={result}
              widget={{
                key: 'json',
              }}
            />
          </DialogContent>
        </ModalDialog>
      </Modal>
    </Stack>
  );
}
