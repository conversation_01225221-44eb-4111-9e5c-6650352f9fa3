import assert from 'node:assert';
import { type MCPServerPackageConfig, type PackagesList } from '@toolsdk.ai/registry/types';
import { mockMCPServerEnv, getToolAppFromMCPServer } from '../dao/mcp-helper';
import { PackageDAO } from '../dao/package-dao';
import { Package as PackagePO } from '../prisma-client';
import { db } from '../dao/db';

import { localMcpServerConfig, type LocalMcpServerConfig } from '../../local-mcp-servers-config';
import { isInCI, getAppEnv } from '@bika/types/system/app-env';
import { AvatarLogo } from '@bika/types/system';
import { McpServerPackageDataBO } from '@toolsdk.ai/sdk-ts/types/bo';
import axios from 'axios';
import _ from 'lodash';

const defaultLogo: AvatarLogo = {
  type: 'COLOR',
  color: 'white',
};

async function fetchGithubLogo(config: MCPServerPackageConfig, currentPO: PackagePO | null): Promise<AvatarLogo> {
  if (getAppEnv() === 'LOCAL' || isInCI() || getAppEnv() === 'SELF-HOSTED') {
    return defaultLogo;
  }

  if (!config.url || !config.url.includes('github.com')) {
    console.warn('No logo found for MCP server config:', config.packageName);
    return defaultLogo;
  }

  const urlParts = config.url.split('/');
  const userName = urlParts[3];
  const headers = process.env.CR_PAT ? { Authorization: `Bearer ${process.env.CR_PAT}` } : {};

  if (currentPO === null || currentPO?.logo === null || _.isEqual(currentPO.logo, defaultLogo)) {
    const response = await axios.get(`https://api.github.com/users/${userName}`, { headers });
    const avatarUrl = response.data.avatar_url;

    return { type: 'URL', url: avatarUrl };
  }

  return currentPO.logo as AvatarLogo;
}

async function fetchGithubReadme(config: MCPServerPackageConfig): Promise<string | undefined> {
  if (getAppEnv() === 'LOCAL' || isInCI() || getAppEnv() === 'SELF-HOSTED') {
    return undefined;
  }

  if (!config.url || !config.url.includes('github.com')) {
    return undefined;
  }

  const urlParts = config.url.split('/');
  const userName = urlParts[3];
  const repoName = urlParts[4];
  const headers = process.env.CR_PAT ? { Authorization: `Bearer ${process.env.CR_PAT}` } : {};

  try {
    if (repoName) {
      const readmeResponse = await axios.get(`https://api.github.com/repos/${userName}/${repoName}/readme`, {
        headers,
      });

      const readme = Buffer.from(readmeResponse.data.content, 'base64').toString('utf-8');
      console.log('Success to fetch README:', `${userName}/${repoName}`);

      return readme;
    }
    return undefined;
  } catch (error) {
    console.warn('Failed to fetch README:', error, `for repo ${userName}/${repoName}`);
  }
}

async function fetchGitHubStar(config: MCPServerPackageConfig): Promise<number | undefined> {
  if (getAppEnv() === 'LOCAL' || isInCI() || getAppEnv() === 'SELF-HOSTED') {
    return undefined;
  }

  if (!config.url || !config.url.includes('github.com')) {
    return undefined;
  }

  const urlParts = config.url.split('/');
  const userName = urlParts[3];
  const repoName = urlParts[4];
  const headers = process.env.CR_PAT ? { Authorization: `Bearer ${process.env.CR_PAT}` } : {};

  try {
    if (repoName) {
      const res = await axios.get(`https://api.github.com/repos/${userName}/${repoName}`, {
        timeout: 5000,
        headers,
      });

      const starCount = res.data.stargazers_count || 0;
      console.log('Success to fetch Star:', `${userName}/${repoName}:${starCount}`);
      return starCount;
    }
    return undefined;
  } catch (error) {
    console.warn('Failed to fetch Star:', error, `for repo ${userName}/${repoName}`);
  }
}

async function fetchGithubInfo(
  config: MCPServerPackageConfig,
  currentPO: PackagePO | null,
): Promise<{ logo: AvatarLogo; readme?: string; starCount?: number }> {
  const [logo, readme, starCount] = await Promise.all([
    fetchGithubLogo(config, currentPO),
    fetchGithubReadme(config),
    fetchGitHubStar(config),
  ]);

  return { logo, readme, starCount };
}

async function doSeedMCPServer(config: LocalMcpServerConfig, developerId: string, cb: (pkg: PackagePO) => void) {
  const key = config.packageName;

  console.log('Seed mcp server ....', key);

  const mockEnv = mockMCPServerEnv(config);
  console.log(`Mock env for ${key}:`, mockEnv);

  const {
    closeConnection,
    tools: mcpTools,
    toolApp,
  } = await getToolAppFromMCPServer(
    'node_modules',
    {
      type: 'MCP_SERVER',
      key: key,
      ...config,
    },
    mockEnv,
  );

  const toolsCount = Object.values(mcpTools).length;
  if (toolsCount === 0) {
    console.warn(`${key} server no tools.`);
    return;
  }

  const packageDataBO: McpServerPackageDataBO = {
    ...config,
    key: key,
    type: 'MCP_SERVER',
  };

  const pkg: PackagePO = await PackageDAO.upsert(developerId, {
    bo: packageDataBO,
    visibility: 'PUBLIC',
    category: config.category,
  });
  assert(pkg);

  await PackageDAO.cache({
    packageId: pkg.id,
    toolapp: toolApp,
    cacheStar: config.star,
  });

  await closeConnection();

  cb(pkg);
}

export async function seedMCPServers(developerId: string, existingKeys: string[]): Promise<string[]> {
  const pkgFullKey: string[] = [];

  // seed local mcp server config
  const configs: LocalMcpServerConfig[] = [...localMcpServerConfig];

  for (const config of configs) {
    await doSeedMCPServer(config, developerId, (pkg) => {
      pkgFullKey.push(pkg.version.length > 0 ? `${pkg.key}:${pkg.version}` : pkg.key);
    });
  }

  // seed @toolsdk.ai/registry
  const packagesList: PackagesList = (await import('@toolsdk.ai/registry/indexes/packages-list.json')).default;
  for (const [k, inf] of Object.entries(packagesList)) {
    // 这个是 registry 的 config，要转一下
    const pkgConfig: MCPServerPackageConfig = (await import('@toolsdk.ai/registry/packages/' + inf.path)).default;

    const config: LocalMcpServerConfig = {
      name: pkgConfig.name || pkgConfig.packageName,
      logo: pkgConfig.logo
        ? {
            type: 'PRESET' as const,
            url: pkgConfig.logo,
          }
        : undefined,
      description: pkgConfig.description || '',
      version: pkgConfig.packageVersion || '',
      runtime: pkgConfig.runtime,
      packageName: pkgConfig.packageName,
      serverConfig: {
        args: pkgConfig.binArgs,
      },
      env: pkgConfig.env || {},
      category: inf.category,
      validated: inf.validated,
      url: pkgConfig.url || '',
    };
    assert(k && pkgConfig && config);

    if (inf.validated) {
      // 验证通过的，再抓取 github 的 logo 和 readme，避免全部抓取导致 seed 过长挂掉
      const packagePO = await db.prisma.package.findUnique({
        where: {
          key_version: {
            key: pkgConfig.packageName,
            version: pkgConfig.packageVersion || '',
          },
        },
      });
      const { logo: overrideLogo, readme, starCount } = await fetchGithubInfo(pkgConfig, packagePO);
      if (!config.logo) {
        config.logo = overrideLogo; // 覆盖 logo，用github 抓到的
      }
      if (readme) {
        config.readme = readme; // 抓到的 README
      }
      if (starCount !== undefined) {
        config.star = starCount; // 抓到的 Star 数量
      }

      try {
        await doSeedMCPServer(config, developerId, (pkg) => {
          pkgFullKey.push(pkg.version.length > 0 ? `${pkg.key}:${pkg.version}` : pkg.key);
        });
      } catch (err) {
        console.error(`[ERROR] Failed to seed MCP server ${k}:`, err);
      }
    } else {
      console.log('invalidated package:', k);

      const fullKey = config.version.length > 0 ? `${config.packageName}:${config.version}` : config.packageName;
      // 验证不通过，且package 已经存在，则跳过
      if (existingKeys.includes(fullKey)) {
        console.warn(`Skipping MCP server ${k} as it already exists in the database.`);
        continue;
      }

      await PackageDAO.upsert(
        developerId,
        {
          bo: {
            ...config,
            key: config.packageName,
            type: 'MCP_SERVER',
          },
          visibility: 'PUBLIC',
          category: config.category,
        },
        false,
      );
      pkgFullKey.push(fullKey);
    }
  }

  return pkgFullKey;
}
