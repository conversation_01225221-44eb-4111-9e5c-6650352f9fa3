// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider      = "prisma-client-js"
  output        = "./prisma-client"
  binaryTargets = ["native", "debian-openssl-3.0.x", "linux-musl-openssl-3.0.x", "debian-openssl-1.1.x", "linux-musl"]
}

datasource db {
  provider = "postgresql"
  url      = env("PG_DATABASE_URL")
}

model Developer {
  id String @id

  // Clerk User ID
  externalId String @unique

  // Clerk auth object and user object
  info    Json
  apiKeys ApiKey[]
  // consumers Consumer[]

  // isDeleted Boolean  @default(false)
  // createdBy String?
  // updatedBy String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // createdComponents ComponentTemplate[] @relation("creator")
  // updatedComponents ComponentTemplate[] @relation("updator")

  accounts Account[]
  // componentInstances ComponentInstance[]

  // triggerInstances TriggerInstance[]
  createdStars Favorite[] @relation("favCreator")

  // updatedFavorites Favorite[] @relation("favoriteUpdator")
  createdPackages       Package[]         @relation("creator")
  updatedPackages       Package[]         @relation("updator")
  createdPackageClients PackageClient[]   @relation("creator")
  updatedPackageClients PackageClient[]   @relation("updator")
  packageInstances      PackageInstance[]
}

// Developer's API Key (Provider)
model ApiKey {
  secretKey String @id

  developerId String
  developer   Developer @relation(fields: [developerId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

enum Visibility {
  PRIVATE
  COMING_SOON
  PUBLIC
}

enum PackageType {
  // N8N_NODE_BASE_NPM

  ZAPIER_JSON
  ZAPIER_NPM

  // ACTIVEPIECES_NPM

  MCP_SERVER
  MCP_CLIENT
  TOOLAPP_JSON
  TOOLAPP_NPM
}

model Account {
  id String @id

  // key，用户自定义的。如 bika.ai 中的 spaceId
  key String

  developerId String
  developer   Developer @relation(fields: [developerId], references: [id])

  // isDeleted Boolean  @default(false)
  createdBy String?
  updatedBy String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // credentials        Credential[]
  tokens           AccountToken[]
  // componentInstances ComponentInstance[]
  // triggerInstances TriggerInstance[]
  packageInstances PackageInstance[]

  configurationInstances ConfigurationInstance[]

  @@unique([developerId, key])
}

model AccountToken {
  token String @id

  accountId String
  account   Account @relation(fields: [accountId], references: [id])

  // { externalId: '' } 支持外部用户ID。如 bika.ai 中的 userId
  metadata Json?

  // isDeleted Boolean  @default(false)
  createdBy String?
  updatedBy String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model ConfigurationInstance {
  id String @id

  packageId String
  package   Package @relation(fields: [packageId], references: [id])

  accountId String
  account   Account @relation(fields: [accountId], references: [id])

  // Fields values (if exist fields)
  // OAuth or Form Values or Basic Auth, etc...
  inputData Json

  testValues      Json?
  connectionLabel String?

  metadata Json?

  // isDeleted Boolean  @default(false)
  createdBy String?
  updatedBy String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  packageInstances PackageInstance[]

  @@index([accountId, packageId])
}

// * @deprecated
// model Credential {
//   id String @id

//   accountId String
//   account   Account @relation(fields: [accountId], references: [id])

//   // Integration relation
//   integrationId String
//   integration   ComponentTemplate @relation(fields: [integrationId], references: [id])

//   componentInstances ComponentInstanceCredential[]

//   // Fields values (if exist fields)
//   // OAuth or Form Values or Basic Auth, etc...
//   inputData Json

//   testValues      Json?
//   connectionLabel String?

//   // callback values and other values
//   // value Json

//   metadata Json?

//   // isDeleted Boolean  @default(false)
//   createdBy String?
//   updatedBy String?
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt

//   @@index([accountId, integrationId])
// }

model PackageCategory {
  id String @id

  key String

  name        Json
  description Json?
  logo        Json?

  // isDeleted Boolean   @default(false)
  createdBy String
  // creator   Developer @relation("creator", fields: [createdBy], references: [id])

  updatedBy String
  // updator   Developer @relation("updator", fields: [updatedBy], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  packages Package[]

  @@unique([key])
}

// Package, includes MCP Server, Zapier App
model Package {
  id String @id

  key     String
  version String @default("")

  status ApprovalStatus @default(APPROVED)

  categoryId String?
  category   PackageCategory? @relation(fields: [categoryId], references: [id])

  packageType PackageType

  name        Json
  description Json?
  logo        Json?
  validated   Boolean @default(false)

  // package 的配置，不放 app
  packageData Json

  // 用户手工设置，是否公开可见
  visibility Visibility
  approvals  Approval[]

  isDeleted Boolean   @default(false)
  createdBy String
  creator   Developer @relation("creator", fields: [createdBy], references: [id])

  updatedBy String
  updator   Developer @relation("updator", fields: [updatedBy], references: [id])

  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  // components      ComponentTemplate[]
  favorites        Favorite[]
  packageInstances PackageInstance[]

  // if undefined, then no cache
  cacheTime DateTime?

  // ToolApp 的 cache，会把function 裁剪调，仅用于界面显示和渲染
  cacheAppData Json?
  // tools Count，根据 cache AppData 的缓存
  cacheCount   Int   @default(0)
  // GitHub Star Count
  cacheStar    Int   @default(0)

  configurationInstances ConfigurationInstance[]
  // cachedComponents       ComponentCached[]

  @@unique([key, version])
}

model PackageClient {
  id  String @id
  key String @unique

  name    Json
  content Json?

  data Json

  isDeleted Boolean   @default(false)
  createdBy String
  creator   Developer @relation("creator", fields: [createdBy], references: [id])

  updatedBy String
  updator   Developer @relation("updator", fields: [updatedBy], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

enum ComponentKind {
  CONFIGURATION
  TOOL
  // ACTION
  TRIGGER
  // INTEGRATION
  WIDGET
  RESOURCE
}

enum ApprovalStatus {
  PENDING
  APPROVED
  REJECTED
}

// 一来关系表，用于记录组件之间的依赖关系，如action依赖integrations
// model ComponentTemplateDependency {
//   // component template id equals
//   id String @id

//   dependId String
//   depend   ComponentTemplate @relation(fields: [dependId], references: [id])

//   // isDeleted Boolean  @default(false)
//   createdBy String?
//   updatedBy String?
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt

//   @@unique([id, dependId])
// }

// model ComponentInstanceCredential {
//   id String @id

//   componentInstanceId String
//   componentInstance   ComponentInstance @relation(fields: [componentInstanceId], references: [id])

//   credentialId String
//   credential   Credential @relation(fields: [credentialId], references: [id])

//   // isDeleted Boolean  @default(false)
//   createdBy         String?
//   updatedBy         String?
//   createdAt         DateTime @default(now())
//   updatedAt         DateTime @updatedAt
//   packageInstanceId String?
// }

// model ComponentInstance {
//   id   String        @id
//   kind ComponentKind

//   // connection accounts' credentials

//   integrationsData Json? // { "openai": "CRENDEIAL_ID"}

//   // input fields values
//   inputData Json

//   developerId String
//   developer   Developer @relation(fields: [developerId], references: [id])

//   // componentId为可选，因为实际用户操作时，一个instance可以更换template
//   componentId String?
//   component   ComponentTemplate? @relation(fields: [componentId], references: [id])

//   consumerKey String?
//   metadata    Json?

//   accountId String?
//   account   Account? @relation(fields: [accountId], references: [id])

//   createdBy String?
//   updatedBy String?
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt

//   credentials ComponentInstanceCredential[]
//   histories   History[]

//   @@unique([kind, accountId, consumerKey])
//   @@index([componentId])
// }

model PackageInstance {
  id String @id

  packageId String?
  package   Package? @relation(fields: [packageId], references: [id])

  // selected tool key
  toolKey   String?
  // input fields values
  inputData Json

  accountId  String
  account    Account @relation(fields: [accountId], references: [id])
  // 冗余
  accountKey String

  consumerKey String?
  metadata    Json?

  configurationInstance   ConfigurationInstance? @relation(fields: [configurationInstanceId], references: [id])
  configurationInstanceId String?

  createdBy String?
  updatedBy String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  histories   History[]
  developer   Developer? @relation(fields: [developerId], references: [id])
  developerId String?

  @@unique([accountId, consumerKey])
}

model Favorite {
  id String @id

  packageId String
  package   Package @relation(fields: [packageId], references: [id])
  // componentTemplateId String
  // componentTemplate   ComponentTemplate @relation(fields: [componentTemplateId], references: [id])

  createdBy String
  creator   Developer @relation("favCreator", fields: [createdBy], references: [id])

  // updatedBy String
  // updator   Developer @relation("favoriteUpdator", fields: [updatedBy], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  // ComponentTemplate   ComponentTemplate? @relation(fields: [componentTemplateId], references: [id])
  // componentTemplateId String?

  @@unique([packageId, createdBy])
}

// 运行历史
// Component Instances Run Histories
model History {
  id String @id

  // TODO: component instances
  instanceId String
  // instance   ComponentInstance @relation(fields: [instanceId], references: [id])

  data Json

  createdBy         String?
  updatedBy         String?
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt
  packageInstance   PackageInstance? @relation(fields: [packageInstanceId], references: [id])
  packageInstanceId String?
}

// 新建或修改后，进入审批、流程
model Approval {
  id String @id

  Package   Package @relation(fields: [packageId], references: [id])
  packageId String

  status ApprovalStatus

  createdBy String?
  updatedBy String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model AttachmentTmp {
  id String @id

  presignedPutUrl String
  path            String

  createdBy String?
  updatedBy String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// 附件
model Attachment {
  id String @id

  // 存放的键值位置
  path String

  // MD5
  etag String

  // 大小
  size Int
  // 扩展名
  ext  String

  // 附件引用计数，被其它地方引用了多少次，每次+1，删除时-1
  // 当理论上一个附件肯定是大于0被引用的，如果小于等于=0，是不是考虑得清理下空间了
  refCount Int @default(0)

  // 缩略图路径,命名通常是 {module}/{path}_thumbnail.{ext}
  thumbnail String?

  // 预览图路径,命名通常是 {module}/{path}_preview.{ext}
  preview String?

  // isDeleted Boolean  @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([etag])
}
