import { Default<PERSON><PERSON><PERSON><PERSON><PERSON>, ToolError<PERSON>enderer } from '@bika/domains/ai/client/chat/tools/default-tool-renderer';
import type { Meta, StoryObj } from '@storybook/react';
import type { ToolInvocation } from '@ai-sdk/ui-utils';
import { ILocaleContext } from '@bika/contents/i18n/context';

const getDefaultData = (state: 'call' | 'result') => ({
  toolInvocation: {
    toolName: 'tool_name',
    toolCallId: 'default-call-id',
    state: state,
    args: {},
  } as ToolInvocation,
  contentProps: {
    icon: undefined,
    call: {
      name: 'call name',
      description: 'In progress',
    },
    result: {
      name: 'result name',
      description: 'Completed',
    },
  },
  localeContext: {
    i: (key: string) => key, // simple translation function for demonstration
  } as ILocaleContext,
});

const defaultResultData = getDefaultData('result');
const defaultCallData = getDefaultData('call');

export default {
  title: '@bika/ai/Tool',
  component: DefaultTool<PERSON>enderer,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  args: defaultResultData,
} satisfies Meta<typeof DefaultToolRenderer>;

type Story = StoryObj<typeof DefaultToolRenderer>;

export const Default: Story = {};

export const NodeIcon = {
  args: {
    ...defaultResultData,
    contentProps: {
      ...defaultResultData.contentProps,
      nodeType: 'AUTOMATION',
    },
  },
};

export const CustomIcon = {
  args: {
    ...defaultResultData,
    contentProps: {
      ...defaultResultData.contentProps,
      icon: {
        kind: 'avatar' as const,
        avatar: {
          type: 'PRESET',
          url: 'https://dev.bika.ai/assets/ai/agent_avatars_recruiter.png',
        },
        name: 'Bika',
      },
    },
  },
};

export const ShowArtifact = {
  args: {
    ...defaultResultData,
    contentProps: {
      ...defaultResultData.contentProps,
      showArtifact: true,
    },
  },
};

export const CallTool = {
  args: defaultCallData,
};

export const HighlightedArtifactTool = {
  args: {
    ...defaultCallData,
    contentProps: {
      ...defaultCallData.contentProps,
      showArtifact: true,
    },
    isHighlight: true,
  },
};

export const ConsultingTools = () => {
  const data = {
    ...defaultResultData,
    toolInvocation: {
      ...defaultResultData.toolInvocation,
      toolName: 'ai-consulting-consultant',
      result: {
        artifact: {},
      },
    },
    contentProps: {
      ...defaultResultData.contentProps,
      showArtifact: true,
    },
  };
  return (
    <>
      <DefaultToolRenderer {...data} hideFlow />
      <DefaultToolRenderer {...data} />
      <DefaultToolRenderer {...data} />
    </>
  );
};

export const ApplyTool = {
  args: {
    ...defaultCallData,
    toolInvocation: {
      ...defaultCallData.toolInvocation,
      args: {
        message: 'Apply for a new tool',
      },
    },
    contentProps: {
      ...defaultCallData.contentProps,
      isAsk: true,
    },
  },
};

export const ErrorTool = () => {
  const data = {
    ...defaultResultData,
    error: 'This is an error message',
  };
  return <ToolErrorRenderer {...data} />;
};
