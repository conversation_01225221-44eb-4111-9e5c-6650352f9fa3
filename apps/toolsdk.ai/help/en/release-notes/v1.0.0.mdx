---
sidebar_position: -909
sidebar_label: v1.0.0
title: v1.0.0 Release Notes
---

# v1.0.0 Release Notes

ToolSDK.ai is a free TypeScript SDK for connecting MCP servers to your agentic AI apps.

If you're a developer of AI agents or automation apps, you've probably felt the pain of integrating and managing multiple MCP servers.
It's messy, hard to deploy, and doesn't scale easily.

ToolSDK.ai makes it simple — connect to over 5,000 MCP servers and AI tools with just one line of code.

Here's what you can do with ToolSDK.ai:

- ⚡ Access running MCP server with OpenAI SDK or Vercel AI SDK in single line of code
- 🤖 Build AI agents that tap into a 10k+ MCP server ecosystem in just one day
- 🛠 Create automation workflows apps (like Zapier, n8n, or Make.com) with forms powered by MCP ecosystem.

You can also add your own MCP servers through the GitHub registry: https://github.com/toolsdk-ai/awesome-mcp-registry


## 🚀 Features

- **ToolSDK.ai SDK**: A TypeScript SDK for connecting to MCP servers and AI tools.
- **MCP Server Hosting**: ToolSDK.ai now hosts thousands MCP servers as MCP-as-a-Service.
- **MCP Hub Server**: Provide a MCP Hub Server, acting as a proxy to connect all hosted MCP servers — one MCP server to rule them all.

## ✨ Highlights

- **Easy Integration**: Instant access to 5000+ hosted MCP servers for popular AI clients like Claude Desktop, Cursor, and OpenAI Desktop.
- **Developer-Friendly**: SDK support for rapid integration of third-party applications and AI tools.
- **MCP Router Server**: Streamlined access to preferred MCP servers via a single configuration point — automatically routes to your starred servers.
- **Easy Connection**: Connect to hosted MCP servers without running local code.
- **Remote MCP Server Access**: Use a single MCP server in clients like Claude or Cursor to access AI tools across the ecosystem.
- **Extensible Architecture**: Support for ToolApp (Zapier-compatible) and MCP Server packages.
- **Secure Authentication**: Credential-based login to ensure secure usage of tools.

## 📖 Documentation

- [Getting Started with ToolSDK.ai?](https://toolsdk.ai/help)

## 📎 Example Project

A basic example project is available to demonstrate how to use ToolSDK.ai with AI SDKs like Vercel AI SDK:

```bash
git clone https://github.com/toolsdk-ai/toolsdk
npm install
npm run example
```

## 📌 Installation

Install the SDK using npm or yarn:
```bash
npm install toolsdk
# or
yarn add toolsdk
```

For more information, see the [ToolSDK.ai Documentation](https://toolsdk.ai/help).