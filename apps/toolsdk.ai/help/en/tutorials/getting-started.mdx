---
title: Getting Started with ToolSDK.ai
slug: toolsdk.ai/help/en/tutorials/getting-started
sidebar_position: 1
sidebar_label: Getting Started with ToolSDK.ai
---


# Getting Started with ToolSDK.ai

## What is ToolSDK.ai?

ToolSDK.ai is a free TypeScript SDK for connecting MCP servers to your agentic AI apps.


If you're a developer of AI agents or automation apps, you've probably felt the pain of integrating and managing multiple MCP servers.
It's messy, hard to deploy, and doesn't scale easily.


ToolSDK.ai makes it simple — connect to over 5,000 MCP servers and AI tools with just one line of code.


Here's what you can do with ToolSDK.ai:

- ⚡ Access running MCP server with OpenAI SDK or Vercel AI SDK in single line of code
- 🤖 Build AI agents that tap into a 10k+ MCP server ecosystem in just one day
- 🛠 Create automation workflows apps (like Zapier, n8n, or Make.com) with forms powered by MCP ecosystem.

You can also add your own MCP servers through the GitHub registry: https://github.com/toolsdk-ai/awesome-mcp-registry

## <b>🚀 </b><b>Quick Start</b>

### <b>AI SDK</b>

This example shows how to use [Vercel AI SDK](https://v5.ai-sdk.dev/docs/getting-started/nodejs) and [ToolSDK.ai](https://www.npmjs.com/package/toolsdk) to create a MCP-Tools-enabled GPT-4.1 assistant:

```ts
import { generateText } from 'ai';
import { createOpenAI } from '@ai-sdk/openai';
import { ToolSDKApiClient } from 'toolsdk/api';

// Initialize Client
const toolSDK = new ToolSDKApiClient({ apiKey: process.env.TOOLSDK_AI_API_KEY });
const openai  = createOpenAI({ apiKey: process.env.OPENAI_API_KEY });

// Get Tools
const searchMCP = await toolSDK.package('@toolsdk.ai/tavily-mcp', {
  TAVILY_API_KEY: process.env.TAVILY_API_KEY,
});
const emailMCP  = await toolSDK.package('@toolsdk.ai/mcp-send-email', {
  RESEND_API_KEY: process.env.RESEND_API_KEY,
});
const searchTool = await searchMCP.getAISDKTool('tavily-search');
const emailTool  = await emailMCP.getAISDKTool('send-email');

// Generate Result with Tools
const completion = await generateText({
  model: openai('gpt-4.1'),
  messages: [{
    role: 'user',
    content: 'Help me search for the latest AI news and send <NAME_EMAIL>',
  }],
  tools: { searchTool, emailTool },
});

console.log(completion);
```

### OpenAI SDK

If you're using the official [OpenAI SDK](https://platform.openai.com/docs/libraries), follow the steps below with [ToolSDK.ai](https://www.npmjs.com/package/toolsdk) to create a MCP-Tools-enabled GPT-4.1 assistant:

```ts
import OpenAI from 'openai';
import { ToolSDKApiClient } from 'toolsdk/api';

// Initialize Client
const toolSDK = new ToolSDKApiClient({ apiKey: process.env.TOOLSDK_AI_API_KEY });
const openai  = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

// Get Tools
const searchMCP = await toolSDK.package('@toolsdk.ai/tavily-mcp', {
  TAVILY_API_KEY: process.env.TAVILY_API_KEY,
});
const emailMCP  = await toolSDK.package('@toolsdk.ai/mcp-send-email', {
  RESEND_API_KEY: process.env.RESEND_API_KEY,
});
const searchTool = await searchMCP.getOpenAISDKTool('tavily-search');
const emailTool  = await emailMCP.getOpenAISDKTool('send-email');

const messages = [{
  role: 'user',
  content: 'Help me search for the latest AI news and send <NAME_EMAIL>',
}];

const completion = await openai.chat.completions.create({
  model: 'gpt-4.1',
  messages,
  tools: [searchTool, emailTool],
});

const toolMap = { 'tavily-search': searchMCP, 'send-email': emailMCP };

// Execute Tool Calls
for (const toolCall of completion.choices[0].message.tool_calls) {
  const { name: toolKey, arguments: argsStr } = toolCall.function;
  const inputData = JSON.parse(argsStr);

  const toolContent = await toolMap[toolKey].run({ toolKey, inputData });

  messages.push(
    { role: 'assistant', tool_calls: [toolCall] },
    { role: 'tool', content: JSON.stringify(toolContent), tool_call_id: toolCall.id },
  );
}

const finalResponse = await openai.chat.completions.create({
  model: 'gpt-4.1',
  messages,
});

console.log(finalResponse);
```

## <b>📘 Playbook</b>

### ✅ Scenario 1: Integrate specific tools (e.g., weather and currency)

```ts
const result = streamText({
  model: openai('gpt-4.1'),
  messages,
  tools: await toolSDK.package('weather-mcp').getAISDKTools(),
});
```

### ✅ Scenario 2: Filter tools by tag (e.g., "finance")

```ts
const result = streamText({
  model: openai('gpt-4.1'),
  messages,
  tools: await toolSDK.package('finance-analyse').getAISDKTools(),
});
```

## <b>Summary</b>

- ToolSDK enables both <b>tool injection</b> and <b>tool resolution</b>, abstracting most of the complexity in orchestrating AI workflows with external APIs.
- You can dynamically fetch, configure, and execute tools without hardcoding endpoint logic.
- All MCP tools are plug-and-play — ideal for multi-agent setups or headless automation builders.

For more advanced examples, visit [ToolSDK.ai Documentation](https://toolsdk.ai) or browse the [Tool Registry](https://github.com/toolsdk-ai/awesome).

## 🧩 Why ToolSDK.ai?

ToolSDK.ai makes it easy to connect MCP Servers and AI tools — get and call with just one line of code.

Just plug in the tools you need and run them inside your AI agents or automation workflows apps.

Perfect for AI agents, automation apps, and rapid prototyping.

## 📚 Explore More

- 📖 Docs: [https://toolsdk.ai/help](https://toolsdk.ai/help)
- 🧰 Tool Registry: [ToolSDK Registry](https://github.com/toolsdk-ai)

