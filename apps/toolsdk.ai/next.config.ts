import type { NextConfig } from 'next';
import createMDX from '@next/mdx';
import rehypeHighlight from 'rehype-highlight';
import remarkGfm from 'remark-gfm';
import json from './package.json' with { type: 'json' };

const nextConfig: NextConfig = {
  /* config options here */
  output: 'standalone',
  pageExtensions: ['js', 'jsx', 'md', 'mdx', 'ts', 'tsx'],
  env: {
    NEXT_PUBLIC_VERSION: json.version,
    VERSION: json.version,
  },
  // 支持跨域
  serverExternalPackages: [
    'n8n-nodes-base',
    'live-plugin-manager',
    '@toolsdk.ai/mcp-server',
    '@toolsdk.ai/plugin-core',
    '@bika.ai/bika-zapier',
    'zapier-platform-core',
    'twitter-api-v2',
  ],
};
const withMDX = createMDX({
  options: {
    // https://github.com/vercel/next.js/issues/71819
    remarkPlugins: process.env.USE_TURBOPACK === 'true' ? [] : [remarkGfm],
    rehypePlugins: process.env.USE_TURBOPACK === 'true' ? [] : [rehypeHighlight],
  },
  // Add markdown plugins here, as desired
});
export default withMDX(nextConfig);
