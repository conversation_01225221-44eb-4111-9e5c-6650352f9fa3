import { AILanguageModelClientConfig } from '@bika/contents/config/client/ai/ai-model-client-config';
import type { Locale } from '@bika/contents/i18n';
import { getServerDictionary } from '@bika/contents/i18n/server';
import { AppCardComponent } from '@bika/domains/integration/client/app-card-component';
import { generateLanguageUrls } from '@bika/domains/shared/client/utils';
import { ListGrid } from '@bika/domains/website/client/blog/list-grid';
import Box from '@mui/joy/Box';
import { iStringParse } from 'basenext/i18n/i-string';
import { Metadata } from 'next';
import React from 'react';

export default async function SkillsetListPage(props: {
  params: Promise<{ lang: string }>;
  searchParams: Promise<{ p: string | undefined }>;
}) {
  const params = await props.params;
  const { p } = await props.searchParams;
  const pageSize = 6; // PresetSkillsetTypes.length;

  const entries = Object.entries(AILanguageModelClientConfig);
  const pageTotal = Math.ceil(entries.length / pageSize);
  const pageNo = p ? parseInt(p, 10) : 1;
  const start = (pageNo - 1) * pageSize;
  const end = start + pageSize;
  // filter 调，如果有 display: 'HIDDEN' 的，则不显示
  const entriesSlice = entries.slice(start, end).filter(([, modelCfg]) => modelCfg.display !== 'HIDDEN');

  return (
    <>
      <ListGrid
        title={'AI Models'}
        description={'AI Models is a collection of AI models, including LLMs, image generation models, and more.'}
        content={
          <>
            {entriesSlice.map(([modelKey, modelCfg], index) => (
              <Box key={index}>
                <AppCardComponent
                  title={iStringParse(modelCfg.name, params.lang as Locale)}
                  description={iStringParse(modelCfg.description, params.lang as Locale)}
                  url={`/${params.lang}/ai-models/$${modelKey}`}
                  logo={modelCfg.logo || { type: 'COLOR', color: 'red' }}
                  logoUrl={undefined}
                  buttonText={'View'}
                />
              </Box>
            ))}
          </>
        }
        totalPages={pageTotal}
        page={pageNo}
        prefixUrl={`${params.lang === 'en' ? '' : `/${params.lang}`}/ai-models`}
      />
    </>
  );
}

export async function generateMetadata(props: { params: Promise<{ lang: string }> }): Promise<Metadata> {
  const params = await props.params;

  const t = getServerDictionary(params.lang as Locale);

  return {
    title: t.website.blog,
    alternates: {
      languages: await generateLanguageUrls(`ai-models`),
    },
  };
}
