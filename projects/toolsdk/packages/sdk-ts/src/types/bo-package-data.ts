import { z } from 'zod';
import { AvatarLogoSchema } from 'basenext/avatar/avatar';
import { iStringSchema } from 'basenext/i18n/i-string';
import { ToolAppAuthenticationSchema } from './bo-configuration';
import { ActionTemplateSchema } from './bo-tool';
import { ToolAppSchema } from './bo-toolapp';
import { ZapierPluginSchema } from './bo-zapier';

// Architecture: Package (MCP Server / MCP Client / Zapier App) → Component (Tool / Action / Configuration / Integration)

// SEO routes:
// https://toolsdk.ai/servers/github    (GitHub servers)
// https://toolsdk.ai/packages/github   (Zapier packages)
// https://toolsdk.ai/apps/github       (Zapier apps)
// https://toolsdk.ai/integrations/github (Zapier integrations)

export const PackageDataTypesSchema = z.enum([
  'ZAPIER_JSON',
  'ZAPIER_NPM',
  'MCP_SERVER',
  'MCP_CLIENT',
  'TOOLAPP_JSON',
  'TOOLAPP_NPM',
]);
export type PackageDataTypes = z.infer<typeof PackageDataTypesSchema>;

export const ToolSDKComponentKinds = ['TOOL', 'CONFIGURATION', 'TRIGGER', 'WIDGET', 'RESOURCE'] as const;

export const ToolSDKComponentKindsSchema = z.enum(ToolSDKComponentKinds);
export type ToolSDKComponentKind = z.infer<typeof ToolSDKComponentKindsSchema>;

// === Component Data ===

export const MCPServerComponentDataSchema = z.object({
  type: z.literal(PackageDataTypesSchema.enum.MCP_SERVER),
  componentType: z.enum(['TOOL', 'CONFIGURATION']),
  name: z.string(),
  description: z.string().optional(),
  // parameters for the config
  env: z.record(z.string()).optional(),
  /**
   * A JSON Schema object defining the expected parameters for the tool.
   */
  inputSchema: z
    .object({
      type: z.literal('object'),
      properties: z.optional(z.object({}).passthrough()),
    })
    .passthrough()
    .optional(),
});
export type MCPServerComponentData = z.infer<typeof MCPServerComponentDataSchema>;

const ZapierJSONComponentDataBOSchema = z.object({
  type: z.literal(PackageDataTypesSchema.enum.ZAPIER_JSON),
  key: z.string(),
  data: ZapierPluginSchema, // ZapierApp // TODO delete ，use server package data
  packageComponentType: z.enum(['creates', 'triggers', 'searches', 'authentication']),
  packageComponentKey: z.string().optional(),
  name: z.string(),
  description: z.string().optional(),
});

const ZapierNPMComponentDataBOSchema = z.object({
  type: z.literal(PackageDataTypesSchema.enum.ZAPIER_NPM),
  key: z.string(),
  packageName: z.string(),
  packageVersion: z.string().optional(),
  packageComponentType: z.enum(['creates', 'triggers', 'searches', 'authentication']),
  packageComponentKey: z.string().optional(),
  name: z.string(),
  description: z.string().optional(),
});

export const ToolSDKAPPJsonComponentDataBOSchema = z.object({
  type: z.literal(PackageDataTypesSchema.enum.TOOLAPP_JSON),
  data: z.discriminatedUnion('componentType', [
    ActionTemplateSchema.extend({
      componentType: z.literal('TOOL'),
    }),
    ToolAppAuthenticationSchema.extend({
      componentType: z.literal('CONFIGURATION'),
    }),
  ]),
});

export const ToolSDKAppNPMComponentDataBOSchema = z.object({
  type: z.literal(PackageDataTypesSchema.enum.TOOLAPP_NPM),
  packageName: z.string(), // redundancy
  appIndex: z.number(), // this package may include an array of multiple apps; returns the index, or may directly represent a single ToolApp
  componentType: z.enum(['TOOL', 'CONFIGURATION']),
  componentKey: z.string(),
  name: iStringSchema,
  description: iStringSchema.optional(),
});

export const ComponentDataBOSchema = z.discriminatedUnion('type', [
  ZapierJSONComponentDataBOSchema,
  ZapierNPMComponentDataBOSchema,
  MCPServerComponentDataSchema,
  ToolSDKAPPJsonComponentDataBOSchema,
  ToolSDKAppNPMComponentDataBOSchema,
]);
export type ComponentDataBO = z.infer<typeof ComponentDataBOSchema>;

// === Package Data ===
const BasePackageDataSchema = z.object({
  key: z.string(),
  version: z.string(), // default ''
  name: iStringSchema,
  description: iStringSchema.optional(),
  logo: AvatarLogoSchema.nullish(),
  star: z.number().default(0).optional(), // GitHub stars
});

export const ZapierJSONPackageDataSchema = BasePackageDataSchema.extend({
  type: z.literal(PackageDataTypesSchema.enum.ZAPIER_JSON),
  data: ZapierPluginSchema,
});
export type ZapierJSONPackageData = z.infer<typeof ZapierJSONPackageDataSchema>;

export const ToolSDKAppJSONPackageDataSchema = BasePackageDataSchema.extend({
  type: z.literal(PackageDataTypesSchema.enum.TOOLAPP_JSON),
  data: ToolAppSchema,
});
export type ToolSDKAppJSONPackageData = z.infer<typeof ToolSDKAppJSONPackageDataSchema>;

export const ToolSDKAppNPMPackageDataBOSchema = BasePackageDataSchema.extend({
  type: z.literal(PackageDataTypesSchema.enum.TOOLAPP_NPM),
  packageName: z.string(),
  packageVersion: z.string().optional(),
  appIndex: z.number().optional(),
});
export type ToolSDKAppNPMPackageDataBO = z.infer<typeof ToolSDKAppNPMPackageDataBOSchema>;

export const ZapierNPMPackageDataSchema = BasePackageDataSchema.extend({
  type: z.literal(PackageDataTypesSchema.enum.ZAPIER_NPM),
  packageName: z.string(),
  packageVersion: z.string().optional(),
});
export type ZapierNPMPackageData = z.infer<typeof ZapierNPMPackageDataSchema>;

/**
 * part from StdioServerParameters
 * @see  https://github.com/modelcontextprotocol/typescript-sdk/blob/main/src/client/stdio.ts#L9
 *
 */
export const MCPServerConfigSchema = z.object({
  command: z.string().optional(),
  args: z.array(z.string()).optional(),
  // env: z.record(z.string()).optional(),
});

export const McpServerPackageDataBOSchema = BasePackageDataSchema.extend({
  type: z.literal(PackageDataTypesSchema.enum.MCP_SERVER),
  runtime: z.enum(['node', 'python', 'java', 'go']), // if undefined, default node js
  // Github Repo URL
  url: z.string().optional(),
  serverConfig: MCPServerConfigSchema.optional(),
  env: z
    .record(
      z.object({
        description: z.string(),
        required: z.boolean(),
      }),
    )
    .optional(),
  // npm package name
  packageName: z.string(),
  readme: z.string().optional(),
  validated: z.boolean().optional(),
});
export type McpServerPackageDataBO = z.infer<typeof McpServerPackageDataBOSchema>;

export const PackageDataBOSchema = z.discriminatedUnion('type', [
  McpServerPackageDataBOSchema,
  ZapierJSONPackageDataSchema,
  ZapierNPMPackageDataSchema,
  ToolSDKAppJSONPackageDataSchema,
  ToolSDKAppNPMPackageDataBOSchema,
]);
export type PackageDataBO = z.infer<typeof PackageDataBOSchema>;
