import type { ToolInvocation } from '@ai-sdk/ui-utils';
import React from 'react';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { SkillsetVO } from '@bika/types/skill/vo';
import { AISkillsetClientRegistry } from '../../../../ai-skillset/client-registry';

export function useToolIcon(skillsetsVOs?: SkillsetVO[]) {
  const getIcon = React.useCallback(
    (skillsets: SkillsetSelectDTO[], toolInvocation: ToolInvocation) => {
      // 从 skillsetsVOs 中获取对应的 skillsetVO, key 和 kind 相同匹配
      let skillsetVO;
      if (!skillsetsVOs) {
        skillsetVO = undefined;
      } else {
        const skillset = skillsetsVOs.find((_skillset) => _skillset.key === toolInvocation.toolName);
        skillsetVO =
          skillset ||
          skillsetsVOs.find((_skillset) => _skillset.skills?.some((sk) => sk.key === toolInvocation.toolName));
      }

      // 特殊 UI 配置
      const skillsetUIMap = AISkillsetClientRegistry.getManySkillsetUI(skillsets);
      const skillUICfg = skillsetUIMap ? skillsetUIMap[toolInvocation.toolName] : undefined;

      if (skillsetVO?.logo) {
        // 从服务端 VO 拿到图标，转换为 INodeIconValue
        return {
          kind: 'avatar' as const,
          avatar: skillsetVO.logo,
          name: skillsetVO.name,
        };
      }
      // 客户端自定义置了 skill icon (不是 skillsets 哦)，覆盖这个图标
      if (skillUICfg?.customIcon) {
        return {
          kind: 'avatar' as const,
          avatar: skillUICfg.customIcon,
        };
      }
      return undefined;
    },
    [skillsetsVOs],
  );

  return { getIcon };
}
