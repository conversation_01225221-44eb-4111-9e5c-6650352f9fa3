import React from 'react';
import {
  getAILanguageModelClientConfigs,
  AILanguageModelClientConfig,
} from '@bika/contents/config/client/ai/ai-model-client-config';
import { useLocale } from '@bika/contents/i18n';
import type { PresetLanguageAIModelDef, IAIModelSelectBO } from '@bika/types/ai/bo';
import { SelectInput } from '@bika/ui/shared/types-form/select-input';
import { OPENAI_MODELS } from './ai-model-custom-manual-openai-select-options';
import { AIModelConfigCustom } from './ai-model-selector-custom';

interface Props {
  value: IAIModelSelectBO;
  onChange: (value: IAIModelSelectBO) => void;
}

export function AIModelsSelector(props: Props) {
  const locale = useLocale();
  const { i } = locale;

  // 一种 preset 优先的表单策略
  const [configType, setConfigType] = React.useState<PresetLanguageAIModelDef | 'custom' | 'auto'>(() => {
    if (props.value.kind === 'custom') {
      return 'custom';
    }
    if (props.value.kind === 'preset') {
      return props.value.model;
    }
    return 'auto';
  });

  const options = React.useMemo(() => {
    const opts = [];
    opts.push({ label: 'Auto', value: 'auto' });

    const configs = getAILanguageModelClientConfigs('agent');
    for (const { key, config } of configs) {
      opts.push({ label: i(config.name), value: key, icon: config.logo });
    }

    opts.push({ label: 'Custom', value: 'custom', icon: AILanguageModelClientConfig.mock.logo });

    return opts;
  }, [i]);

  return (
    <>
      <SelectInput
        label={'AI Model'}
        options={options}
        onChange={(newVal) => {
          if (!newVal) {
            throw new Error('AI Model is required');
          }

          if (newVal === 'custom') {
            setConfigType('custom');
            props.onChange({
              ...props.value,
              kind: 'custom',
              custom: {
                type: 'manual',
                provider: {
                  type: 'OPENAI',
                  apiKey: '',
                  baseUrl: undefined,
                },
                modelId: OPENAI_MODELS[0],
              },
            });
          } else if (newVal === 'auto') {
            setConfigType(newVal);
            props.onChange({
              ...props.value,
              kind: 'auto',
            });
          } else {
            setConfigType(newVal);
            props.onChange({
              ...props.value,
              kind: 'preset',
              model: newVal as PresetLanguageAIModelDef,
            });
          }
        }}
        value={configType}
      />

      {props.value.kind === 'custom' && (
        <AIModelConfigCustom
          value={props.value}
          onChange={(newVal) => {
            props.onChange(newVal);
          }}
        />
      )}
    </>
  );
}
