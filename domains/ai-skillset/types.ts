import type { ToolInvocation } from '@ai-sdk/ui-utils';
import type { ToolApp } from '@toolsdk.ai/sdk-ts/types/bo';
import type { ToolSet, DataStreamWriter } from 'ai';
import type React from 'react';
import type { IApiCallerContext } from '@bika/api-caller';
import type { ILocaleContext } from '@bika/contents/i18n';
import type { SpaceSO } from '@bika/domains/space/server/space-so';
import type { AIIntentUIResolveDTO } from '@bika/types/ai/dto';
import { INodeIconValue } from '@bika/types/node/bo';
import type { NodeCreateDTO } from '@bika/types/node/dto';
import type { NodeTreeVO } from '@bika/types/node/vo';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import type { AvatarLogo, iString } from '@bika/types/system';
import type { NodeResourceIconType } from '@bika/ui/node/icon';
import type { UserSO } from '../user/server/user-so';

export type IToolUIShared = {
  executeToolResult: (toolCallId: string) => Promise<any>;
  // 使用于交式工具
  addToolResult: (userResult: any) => void;
  skillsets: SkillsetSelectDTO[];
  isHighlight?: boolean;
  hideFlow?: boolean;
};
/**
 * 
 * 
```mermaid
graph TD

UI[UI技能选择器] --> A["Skillset 技能集 (SkillsetVO)"] --> B["预置ToolSet(AI SDK)"]
B --> C[Native Toolset]
B --> D[ToolSDK.ai Local Compatible MCP Toolset]
A --> E[Automation Action]
A --> F[ToolSDK ]
A --> G[Custom]
G --> H[Custom MCP Server]

C --> Y["传入AI SDK tools"]
D --> Y
E --> Y
F --> Y
H --> Y

Y --> Z["转换OpenAI SDK tools"]

C --> X["转换 Automation Action BO"]
D --> X
E --> X
F --> X
H --> X
```

 */
export type ToolUIComponentProps = IToolUIShared & {
  toolInvocation: ToolInvocation;
  localeContext: ILocaleContext;
  skillsets: SkillsetSelectDTO[];
  // 通常是被选中
  onClickTool?: () => void;

  sendMessage?: (msg: string) => Promise<void>;

  /**
   *
   * @param uiResolve
   * @returns
   *
   * @deprecated 历史遗留原因，Wizard 的 UIResolveDTO
   */
  sendUI?: (uiResolve: AIIntentUIResolveDTO) => Promise<void>;
};

export type ArtifactUIProps = Pick<ToolUIComponentProps, 'toolInvocation' | 'localeContext' | 'skillsets'>;

export type ArtifactUIComponent = React.FC<ArtifactUIProps>;

export type ToolUIContentProps = {
  // Tool's Display Name
  displayName?: string;

  isAsk?: boolean; // 是否是 Ask Tool
  nodeType?: NodeResourceIconType;
  icon?: INodeIconValue;
  call?: {
    name: iString;
    description: iString;
  };
  result?: {
    name: iString;
    description: iString;
  };
};

export type FullToolUIProps = ToolUIComponentProps & ToolUIContentProps;

export type ToolUIComponent = (props: ToolUIComponentProps) => React.ReactElement | null;
export type ToolUIConfig = (props: ToolUIComponentProps) => ToolUIContentProps;

export type NodeCreateDTOWithTemplateName = NodeCreateDTO & {
  templateName?: string;
};

export type ClientExecuteContext = {
  apiCaller?: IApiCallerContext;
  setData: (data: {
    toolInvocation: ToolInvocation | null;
    dto: NodeCreateDTOWithTemplateName[];
    process: number;
  }) => void;
  setRootNode: React.Dispatch<React.SetStateAction<NodeTreeVO | undefined>>;
};

export type SkillsetUIMap = Record<
  string,
  {
    // Friendly name，请注意，以下是对 skill 本体针对性的定制名字、组件、icon，其中默认的 icon，默认取 Skillset VO
    displayName?: iString;
    component?: ToolUIComponent | ToolUIConfig;
    // 默认会用 Skillset VO 的 icon，这里会覆盖这个 skill 的 icon
    customIcon?: AvatarLogo; // React.ReactNode;

    // 客户端执行 tool 的 result
    clientExecute?: (toolInvocation: ToolInvocation, context?: ClientExecuteContext) => Promise<void>;
    // 选择 artifact 的模式
    artifact?:
      | 'ai-ui' // AIGC Artifact: 自动 AI 生成一个 UI 控件，掉trpc 获取
      | 'server-artifact' //  服务端 Artifact:  服务端实现，streaming 写到 message annotation 里，Tool 执行的 result，必须带有 artifactId，会自动读取，实现控件
      | ArtifactUIComponent; // 纯前端 Artifact
  }
>;

export type PresetToolsetConfigBO = {
  type: 'bika-native';
  logo?: AvatarLogo;
  name: iString;
  description?: iString;
  display?: 'HIDDEN' | 'SHOW' | 'COMING_SOON';
};
// | { type: 'mcp-local'; config: LocalMcpServerConfig; env?: Record<string, string> }
// | { type: 'toolsdk'; key: string; env?: Record<string, string>; includeTools?: string[] };
// | { type: 'custom'; custom: () => Promise<ToolSet> };

/**
 * Skillset Handler Context
 */
export type SkillsetHandlerContext = {
  // Request User
  user: UserSO;
  // eslint-disable-next-line no-undef
  space?: SpaceSO;
  dataStreamWriter?: DataStreamWriter;
  configuration?: Record<string, unknown>;
};

// ToolSet = AI SDK 的 ToolSet，实际我们返回的，是被我们重整过的 ToolSet，不会服务端执行；
export type ToolSetHandler = (ctx: SkillsetHandlerContext) => Promise<ToolSet>;

export type Skillset = ToolApp & { toolsetHandler?: ToolSetHandler };
export type SkillsetHandler = (ctx: SkillsetHandlerContext) => Promise<Skillset>;
