'use client';

import dayjs from 'dayjs';
import Image from 'next/image';
import React, { useRef, useEffect } from 'react';
import { CTA } from '@bika/contents/docs/components/cta';
import { iStringParse, Locale } from '@bika/types/i18n/bo';
import type { TemplateCardInfoVO } from '@bika/types/template/vo';
import { Stack } from '@bika/ui/layouts';
import { MarkdownBody, MarkdownHTML } from '@bika/ui/markdown';
import { Text } from '@bika/ui/text';
import { Typography } from '@bika/ui/website/typography/index';
import TemplateBlock from './landing-page/template/index';

interface Props {
  locale?: Locale;
  meta?: {
    title: string;
    author: string;
    date?: Date;
    cover: string;
  };
  // markdown 已被转换成html的内容
  html?: string;
  // 如果使用react组件，会处理server component、client component、markdown component等重叠问题，谨慎使用
  children?: React.ReactNode;
  toc?: {
    name: string;
    href: string;
    level: 1 | 2 | 3 | 4;
  }[];

  // 前面插入内容
  beforeContent?: React.ReactNode;

  // 最后面插入内容
  afterContent?: React.ReactNode;

  // 相关链接
  relatedLinks?: { url: string; text: string }[];

  templateInfos?: TemplateCardInfoVO[];

  customCTA?: string | null;
}

/**
 * 文章内容入口，支持传入HTML或Markdown纯文本
 *
 * blog, help, template等文章的内容展示
 *
 * @param props
 * @returns
 */
export function ArticleContent(props: Props) {
  const { html, toc, locale } = props;
  const { date, title, author, cover } = props.meta || {};

  const content = html || 'No content';

  const ref = useRef<HTMLDivElement>(null);
  // 根据字数计算阅读时间 只返回分钟
  const readTime = Math.ceil(content.length / 3000);

  useEffect(() => {
    const handleScroll = () => {
      if (ref.current && window.scrollY > (props.meta ? 480 : 100)) {
        ref.current.style.position = 'fixed';
        ref.current.style.top = '80px';
      } else if (ref.current) ref.current.style.position = 'static';
      // linkVisted
    };

    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            const src = img.getAttribute('data-src');
            if (src) {
              img.src = src;
              img.removeAttribute('data-src');
              observer.unobserve(img);
            }
          }
        });
      },
      {
        root: null,
        rootMargin: '50px',
        threshold: 0.1,
      },
    );

    const images = document.querySelectorAll('img[data-src]');
    images.forEach((img) => observer.observe(img));

    return () => {
      images.forEach((img) => observer.unobserve(img));
    };
  }, [html]);

  const minLevel = Math.min(...(toc || []).map((item) => item.level));
  return (
    <Stack sx={{ padding: { xs: 0, sm: 2 } }}>
      {props.meta && (
        <Stack direction="row">
          <Stack
            sx={{
              width: { xs: '100%', sm: 300 },
              height: { xs: 375, sm: 300 },
              position: 'relative',
              borderRadius: { xs: 0, sm: 16 },
              flexShrink: 0,
            }}
          >
            <Image
              src={cover || '/assets/images/default_blog_banner.png'}
              alt={title || ''}
              objectFit="cover"
              layout="fill"
              style={{ borderRadius: 'inherit' }}
            />
          </Stack>

          <Stack
            direction="column"
            sx={{
              height: { xs: 375, sm: 'auto' },
              width: { xs: '100%', sm: 'auto' },
              position: { xs: 'absolute', sm: 'static' },
              marginLeft: { xs: 0, sm: 5 },
              p: { xs: 3, sm: 0 },
              justifyContent: { xs: 'flex-end', sm: 'flex-start' },
            }}
          >
            <Stack component="h1" mt={5}>
              <Typography tag="span" level={3} color="var(--text-primary)">
                {title || 'Bika.ai'}
              </Typography>
            </Stack>
            <Stack direction="row" mt={2}>
              {/* author */}
              <Stack
                px={1}
                sx={{ borderRight: '1px solid var(--border-default)' }}
                spacing={1}
                direction="row"
                alignItems="center"
              >
                <Image src="/assets/icons/website/author.svg" alt="author" width={16} height={16} />
                <Text level={4}>{author || 'Bika'}</Text>
              </Stack>

              {/* date */}
              {date && (
                <Stack
                  px={1}
                  sx={{ borderRight: '1px solid var(--border-default)' }}
                  spacing={1}
                  direction="row"
                  alignItems="center"
                >
                  <Image src="/assets/icons/website/date.svg" alt="date" width={16} height={16} />
                  {/* May 23, 2024 */}
                  <Text level={4}>{dayjs(date).format('MMMM DD, YYYY')}</Text>
                </Stack>
              )}

              <Stack px={1} spacing={1} direction="row" alignItems="center">
                <Image src="/assets/icons/website/time.svg" alt="date" width={16} height={16} />
                {/* May 23, 2024 */}
                <Text level={4}>{readTime} min read</Text>
              </Stack>
            </Stack>
          </Stack>
        </Stack>
      )}

      <Stack
        mt={props.meta ? 5 : 0}
        mb={10}
        direction="row"
        sx={{
          padding: { xs: 2, sm: 0 },
        }}
      >
        {/* Markdown start */}
        <Stack flex={1} spacing={2}>
          {props.beforeContent}
          {props.html && <MarkdownHTML html={content} />}
          {props.children && props.children}
          {props.afterContent}

          {props.customCTA !== null && <CTA locale={locale || 'en'} customCTA={props.customCTA} />}

          {props.relatedLinks && (
            <>
              <Typography level={3}>
                {iStringParse(
                  {
                    en: 'Recommend Reading',
                    'zh-CN': '推荐阅读',
                    'zh-TW': '推薦閱讀',
                    ja: 'おすすめの読み物',
                  },
                  locale,
                )}
              </Typography>
              <MarkdownBody>
                <ul>
                  {props.relatedLinks.map((link, index) => (
                    <li key={index}>
                      <a target="_blank" href={link.url}>
                        {link.text || link.url}
                      </a>
                    </li>
                  ))}
                </ul>
              </MarkdownBody>
            </>
          )}

          {props.templateInfos && (
            <TemplateBlock
              data={{
                data: props.templateInfos,
                title: iStringParse(
                  {
                    en: 'Recommend AI Automation Templates',
                    'zh-CN': '推荐AI自动化模板',
                    'zh-TW': '推薦AI自動化模板',
                    ja: 'AI自動化テンプレートをお勧めします',
                  },
                  locale,
                ),
                type: 'template',
                clickMode: 'template',
                columns: 2,
              }}
            />
          )}
        </Stack>
        {toc && (
          <Stack ml={6} sx={{ width: 250, display: { xs: 'none', sm: 'block' } }}>
            <div ref={ref} style={{ position: 'static', overflow: 'auto', maxHeight: '90vh', width: 250 }}>
              {toc.map((item) => (
                <Stack key={item.href} direction="row" alignItems="center" spacing={1} mt={2}>
                  <Text level={item.level}>
                    <Stack
                      pl={(item.level - minLevel) * 1.5}
                      direction="row"
                      component="a"
                      href={`#${item.href}`}
                      sx={{
                        ':hover': {
                          color: 'var(--linkHover)',
                        },
                        ':active': {
                          color: 'var(--linkActive)',
                        },
                      }}
                      onClick={(e) => {
                        // 滚到这个href的位置-50px 慢慢滚动
                        e.preventDefault();
                        const target = document.getElementById(item.href);
                        if (target) {
                          window.scrollTo({
                            top: target.getBoundingClientRect().top + window.scrollY - 50,
                            behavior: 'smooth',
                          });
                        }
                      }}
                    >
                      {/* <Stack mr={1} sx={{ fontSize: 10 }}>
                          H{item.level}
                        </Stack> */}
                      {item.name}
                    </Stack>
                  </Text>
                </Stack>
              ))}
            </div>
          </Stack>
        )}
      </Stack>
    </Stack>
  );
}
